// lib/screens/register_screen.dart
import 'package:flutter/material.dart';
import '../services/api.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'dart:convert';
import '../widgets/navigation_bar.dart';
import '../widgets/footer.dart';
import '../constants/theme_constants.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final email = TextEditingController();
  final pass = TextEditingController();
  final username = TextEditingController();

  bool loading = false;
  bool _obscurePassword = true;

  Future<void> _submit() async {
    final e = email.text.trim();
    final p = pass.text;
    final u = username.text.trim();

    if (e.isEmpty || p.isEmpty || u.isEmpty) {
      Fluttertoast.showToast(msg: 'Fill all required fields');
      return;
    }

    final requestBody = json.encode({
      "email": e,
      "password": p,
      "username": u,
    });

    // Debug
    print("Flutter Console: Request body = $requestBody");

    setState(() => loading = true);
    try {
      final response = await Api.register(e, p, u);

      // Debug
      print("Flutter Console: Response status = ${response.statusCode}");
      print("Flutter Console: Response body = ${response.body}");

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final token = responseData['token'] as String?;
        final user = responseData['user'];

        if (token != null && user != null) {
          final userId = user['id']?.toString();
          final username = user['username'] as String?;

          if (userId != null && username != null) {
            await Api.saveAuth(token, userId, username);
            Fluttertoast.showToast(msg: 'Registered successfully!');
            if (mounted)
              Navigator.pushNamedAndRemoveUntil(
                context,
                '/home',
                (route) => false, // Remove all previous routes
                arguments: username,
              );
          } else {
            Fluttertoast.showToast(
                msg: 'Registration failed: Invalid response data');
          }
        } else {
          Fluttertoast.showToast(
              msg: 'Registration failed: Invalid response data');
        }
      } else {
        Fluttertoast.showToast(
            msg: response.body.isNotEmpty
                ? response.body
                : 'Registration failed');
      }
    } catch (err) {
      print("Flutter Console: Error during registration: $err");
      Fluttertoast.showToast(msg: 'Error: $err');
    } finally {
      if (mounted) setState(() => loading = false);
    }
  }

  @override
  Widget build(BuildContext ctx) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: const PreferredSize(
        preferredSize: Size.fromHeight(60),
        child: AppNavigationBar(),
      ),
      body: Column(
        children: [
          Expanded(
            child: Container(
              width: double.infinity,
              color: AppColors.roseGold, // Rose Gold background
              child: Center(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Container(
                    constraints: const BoxConstraints(maxWidth: 600),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset('assets/logo.png',
                            height: 120), // Slightly larger logo
                        const SizedBox(height: 24),
                        const Text(
                          'Create Hashtag Dollars Account',
                          style: AppTextStyles.title, // Enlarged from 22 to 24
                        ),
                        const SizedBox(height: 24),
                        TextField(
                          controller: email,
                          keyboardType: TextInputType.emailAddress,
                          style: AppTextStyles.inputText,
                          decoration: AppInputDecorations.primaryInput(
                            hintText: 'Email',
                          ),
                        ),
                        const SizedBox(height: 16),
                        TextField(
                          controller: pass,
                          obscureText: _obscurePassword,
                          style: AppTextStyles.inputText,
                          decoration: AppInputDecorations.primaryInput(
                            hintText: 'Password',
                            suffixIcon: IconButton(
                              icon: Icon(
                                _obscurePassword
                                    ? Icons.visibility_off
                                    : Icons.visibility,
                                color: AppColors.inputHint,
                              ),
                              onPressed: () {
                                setState(() {
                                  _obscurePassword = !_obscurePassword;
                                });
                              },
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        TextField(
                          controller: username,
                          style: AppTextStyles.inputText,
                          decoration: AppInputDecorations.primaryInput(
                            hintText: 'Username (without #\$)',
                          ),
                        ),
                        const SizedBox(height: 24),
                        SizedBox(
                          width: double.infinity,
                          child: loading
                              ? const Center(child: CircularProgressIndicator())
                              : ElevatedButton(
                                  onPressed: _submit,
                                  style: AppButtonStyles.primaryButton,
                                  child: const Text('Create Account'),
                                ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Usernames will automatically receive the #\$ prefix on the platform.',
                          style:
                              AppTextStyles.bodySmall, // Enlarged from 12 to 14
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
          const AppFooter(),
        ],
      ),
    );
  }
}
