# Database
# SQLite database is created automatically

# JWT Secret
JWT_SECRET=your_jwt_secret_here

# Server Configuration
PORT=4242
FRONTEND_URL=http://localhost:3000

# SMTP Configuration (for password reset emails)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
SMTP_FROM_EMAIL=<EMAIL>

# Stripe Configuration (if using Stripe)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# AWS Configuration (if using SES instead of SMTP)
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# AWS_REGION=us-east-1