import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../services/api.dart';

class PaymentHistoryScreen extends StatefulWidget {
  final String username;
  const PaymentHistoryScreen({super.key, required this.username});

  @override
  State<PaymentHistoryScreen> createState() => _PaymentHistoryScreenState();
}

class _PaymentHistoryScreenState extends State<PaymentHistoryScreen> {
  List<dynamic> payments = [];
  List<dynamic> filteredPayments = [];
  bool loading = false;
  int page = 1;
  final int pageSize = 20;
  String searchQuery = '';
  String filterType = 'All'; // e.g., All, topup, donation

  final searchCtrl = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadPayments();
  }

  Future<void> _loadPayments() async {
    setState(() => loading = true);
    try {
      final allPayments = await Api.getPaymentHistory(widget.username);
      setState(() {
        payments = allPayments ?? [];
        _applyFilters();
      });
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error loading payments: $e');
    } finally {
      setState(() => loading = false);
    }
  }

  void _applyFilters() {
    List<dynamic> temp = List.from(payments);

    // Filter by search
    if (searchQuery.isNotEmpty) {
      temp = temp.where((p) {
        final username = (p['username'] ?? '').toString().toLowerCase();
        final sessionId = (p['session_id'] ?? '').toString().toLowerCase();
        return username.contains(searchQuery.toLowerCase()) ||
            sessionId.contains(searchQuery.toLowerCase());
      }).toList();
    }

    // Filter by type
    if (filterType != 'All') {
      temp = temp.where((p) => (p['type'] ?? '') == filterType).toList();
    }

    setState(() {
      filteredPayments = temp;
      page = 1; // reset to first page when filters change
    });
  }

  List<dynamic> _getPaginatedItems() {
    final start = (page - 1) * pageSize;
    final end = (start + pageSize) > filteredPayments.length
        ? filteredPayments.length
        : (start + pageSize);
    return filteredPayments.sublist(start, end);
  }

  void _nextPage() {
    if (page * pageSize < filteredPayments.length) {
      setState(() => page++);
    }
  }

  void _prevPage() {
    if (page > 1) setState(() => page--);
  }

  @override
  Widget build(BuildContext context) {
    final paginated = _getPaginatedItems();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment History'),
      ),
      body: loading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                children: [
                  // Search Bar
                  TextField(
                    controller: searchCtrl,
                    decoration: InputDecoration(
                      labelText: 'Search by username or session',
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          searchCtrl.clear();
                          setState(() {
                            searchQuery = '';
                            _applyFilters();
                          });
                        },
                      ),
                    ),
                    onChanged: (val) {
                      setState(() {
                        searchQuery = val;
                        _applyFilters();
                      });
                    },
                  ),
                  const SizedBox(height: 12),

                  // Filter Dropdown
                  Row(
                    children: [
                      const Text('Filter by type:'),
                      const SizedBox(width: 12),
                      DropdownButton<String>(
                        value: filterType,
                        items: ['All', 'topup', 'donation']
                            .map((f) => DropdownMenuItem(
                                  value: f,
                                  child: Text(f),
                                ))
                            .toList(),
                        onChanged: (val) {
                          if (val != null) {
                            setState(() {
                              filterType = val;
                              _applyFilters();
                            });
                          }
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Payment list
                  Expanded(
                    child: paginated.isEmpty
                        ? const Center(child: Text('No payments found.'))
                        : ListView.builder(
                            itemCount: paginated.length,
                            itemBuilder: (_, idx) {
                              final p = paginated[idx];
                              final amt = (p['amount'] ?? 0) / 100.0;
                              final ts = p['created_at'] ?? 0;
                              final when = DateTime.fromMillisecondsSinceEpoch(ts);
                              return ListTile(
                                leading: Icon(
                                  p['type'] == 'topup'
                                      ? Icons.add_circle
                                      : Icons.compare_arrows,
                                ),
                                title: Text('\$${amt.toStringAsFixed(2)}'),
                                subtitle: Text(
                                    '${p['type'] ?? ''} • ${when.toLocal()}'),
                              );
                            },
                          ),
                  ),

                  // Pagination controls
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('Page $page of ${((filteredPayments.length - 1) ~/ pageSize) + 1}'),
                      Row(
                        children: [
                          IconButton(
                              onPressed: _prevPage, icon: const Icon(Icons.arrow_back)),
                          IconButton(
                              onPressed: _nextPage, icon: const Icon(Icons.arrow_forward)),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
    );
  }
}
