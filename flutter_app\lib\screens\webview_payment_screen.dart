import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:fluttertoast/fluttertoast.dart';

class WebViewPaymentScreen extends StatefulWidget {
  final String paymentUrl;
  final String successUrl;
  final String cancelUrl;

  const WebViewPaymentScreen({
    super.key,
    required this.paymentUrl,
    required this.successUrl,
    required this.cancelUrl,
  });

  @override
  State<WebViewPaymentScreen> createState() => _WebViewPaymentScreenState();
}

class _WebViewPaymentScreenState extends State<WebViewPaymentScreen> {
  late final WebViewController controller;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setUserAgent('Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1')
      ..enableZoom(true)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() => isLoading = true);
            print('WebView: Page started loading: $url');
          },
          onPageFinished: (String url) {
            setState(() => isLoading = false);
            print('WebView: Page finished loading: $url');
          },
          onWebResourceError: (WebResourceError error) {
            print('WebView: Resource error: ${error.description}');
          },
          onNavigationRequest: (NavigationRequest request) {
            final url = request.url;
            print('WebView: Navigation request to: $url');

            // Check for success redirect
            if (url.startsWith('hashtagdollars://payment-success')) {
              final uri = Uri.parse(url);
              final sessionId = uri.queryParameters['session_id'];

              Navigator.of(context).pop({
                'success': true,
                'sessionId': sessionId,
              });
              return NavigationDecision.prevent;
            }

            // Check for cancel redirect
            if (url.startsWith('hashtagdollars://payment-cancel')) {
              Navigator.of(context).pop({'success': false});
              return NavigationDecision.prevent;
            }

            // Check for Stripe onboarding completion
            if (url.contains('/stripe-return') ||
                url.contains('stripe-return')) {
              Navigator.of(context).pop({'success': true});
              return NavigationDecision.prevent;
            }

            // Check for Stripe onboarding refresh/cancel
            if (url.contains('/stripe-refresh') ||
                url.contains('stripe-refresh')) {
              Navigator.of(context).pop({'success': false});
              return NavigationDecision.prevent;
            }

            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.paymentUrl));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop({'success': false}),
        ),
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: controller),
          if (isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}
