import 'package:flutter/material.dart';
import 'package:hashtag_dollars/constants/theme_constants.dart';

class Customiconbutton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String label;
  final IconData icon;
  const Customiconbutton({
    super.key,
    required this.onPressed,
    this.label = '',
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onPressed,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: AppColors.warningText),
          const SizedBox(height: 4),
          Text(label, style: Theme.of(context).textTheme.bodySmall),
        ],
      ),
    );
  }
}
