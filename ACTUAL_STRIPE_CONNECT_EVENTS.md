# 🎯 Actual Stripe Connect Events (2024)

## ✅ **Exact Events Our Code Expects**

Based on our webhook handler in `backend/routes/stripe-connect.js`, here are the **exact event names** you need to select:

### **Required Events for Connect Webhook:**

1. **`account.updated`** 
   - Triggers when user completes verification
   - Updates onboarding status in our database

2. **`transfer.created`**
   - Triggers when cash out transfer is initiated
   - Updates payout status to "processing"

3. **`transfer.paid`**
   - Triggers when cash out transfer completes successfully
   - Updates payout status to "completed"

4. **`transfer.failed`**
   - Triggers when cash out transfer fails
   - Updates payout status to "failed" with error message

## 🔍 **How to Find These Events in Stripe Dashboard**

### **Step 1: Enable Connect First**
1. Go to **Stripe Dashboard** → **Connect**
2. Click **"Get started"** if not already enabled
3. Complete the platform setup

### **Step 2: In Webhook Setup**
When creating the webhook, look for these **exact names**:

- ✅ `account.updated`
- ✅ `transfer.created` 
- ✅ `transfer.paid`
- ✅ `transfer.failed`

### **Step 3: Event Categories**
These events are typically found under:
- **"Connect"** category
- **"Account"** category  
- **"Transfer"** category

## 🎯 **Current Stripe Connect Event Structure (2024)**

According to Stripe's current documentation:

### **Account Events:**
- `account.updated` - ✅ **We need this**
- `account.application.deauthorized`
- `account.external_account.created`
- `account.external_account.deleted`
- `account.external_account.updated`

### **Transfer Events:**
- `transfer.created` - ✅ **We need this**
- `transfer.paid` - ✅ **We need this**  
- `transfer.failed` - ✅ **We need this**
- `transfer.reversed`
- `transfer.updated`

### **Capability Events:**
- `capability.updated`

### **Person Events (for Individual accounts):**
- `person.created`
- `person.deleted`
- `person.updated`

## 🚀 **Webhook Setup Instructions**

### **Create Connect Webhook:**
1. **Endpoint URL:** `https://your-ngrok-url.ngrok.io/api/stripe-connect/webhook`
2. **Description:** `Hashtag Dollar Connect Events`
3. **Select these 4 events:**
   - ✅ `account.updated`
   - ✅ `transfer.created`
   - ✅ `transfer.paid`
   - ✅ `transfer.failed`

### **If Events Are Missing:**

#### **Account Events Not Visible:**
- Make sure **Connect is enabled** in your Stripe account
- Check you're in **Test mode**
- Look under **"Connect"** or **"Account"** categories

#### **Transfer Events Not Visible:**
- These appear after you create your first Express account
- Try creating a test Connect account first
- Look under **"Transfer"** or **"Payout"** categories

## 🔧 **Alternative Event Names**

If the exact names aren't available, look for these alternatives:

### **Instead of `account.updated`:**
- `capability.updated`
- `person.updated`

### **Instead of `transfer.*` events:**
- `payout.created`
- `payout.paid`
- `payout.failed`

## 🎯 **What Each Event Does in Our App**

### **`account.updated`:**
```javascript
// Updates user verification status
const onboarded = account.details_submitted && account.charges_enabled && account.payouts_enabled;
db.prepare('UPDATE users SET stripe_onboarded = ? WHERE stripe_account_id = ?')
  .run(onboarded ? 1 : 0, account.id);
```

### **`transfer.created`:**
```javascript
// Updates payout status to processing
db.prepare('UPDATE payouts SET status = ? WHERE stripe_transfer_id = ?')
  .run('processing', transfer.id);
```

### **`transfer.paid`:**
```javascript
// Updates payout status to completed
db.prepare('UPDATE payouts SET status = ? WHERE stripe_transfer_id = ?')
  .run('completed', transfer.id);
```

### **`transfer.failed`:**
```javascript
// Updates payout status to failed with error
db.prepare('UPDATE payouts SET status = ?, error_message = ? WHERE stripe_transfer_id = ?')
  .run('failed', transfer.failure_message, transfer.id);
```

## 🎯 **Testing the Events**

After setting up webhooks, test each event:

1. **Test `account.updated`:** Complete Stripe verification for a user
2. **Test `transfer.*`:** Initiate a cash out from the app
3. **Check backend logs** for webhook confirmations
4. **Verify database updates** for status changes

---

## 🚀 **Ready to Set Up!**

Use these **exact event names** in your Stripe webhook configuration:
- `account.updated`
- `transfer.created`
- `transfer.paid`
- `transfer.failed`

**These are the current, official Stripe Connect event names that our code expects!** ✅