import 'dart:convert';
import 'package:universal_html/html.dart' as html;
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:http/http.dart' as http;
import '../constants/theme_constants.dart';
import '../services/api.dart';
import '../widgets/enhanced_share_widget.dart';
import 'cashout_screen.dart';

class SocialProfileScreen extends StatefulWidget {
  final String fromUsername; // logged-in user (giver)
  final String toUsername; // profile owner (receiver)

  const SocialProfileScreen({
    super.key,
    required this.fromUsername,
    required this.toUsername,
  });

  @override
  State<SocialProfileScreen> createState() => _SocialProfileScreenState();
}

class _SocialProfileScreenState extends State<SocialProfileScreen> {
  Map<String, dynamic> receiverUser = {}; // The profile being viewed (receiver)
  Map<String, dynamic> senderUser = {}; // The current logged-in user (sender)
  bool loading = true;
  double customAmount = 0.25;
  double balance = 0.0;
  Uint8List? receiverImageBytes; // Profile image of the receiver
  Uint8List? senderImageBytes; // Profile image of the sender
  bool isSharedLink = false;
  // donation history + favorites
  List<Map<String, dynamic>> givers30Days = [];
  double totalReceived30Days = 0.0;
  List<Map<String, dynamic>> favoriteGivers = [];

  bool showShareIcons = false; // after donation

  @override
  void initState() {
    super.initState();
    _load();
    _loadDonationHistory();
    if (widget.fromUsername.isNotEmpty) {
      _loadFavorites();
    }
  }

  Future<void> _load() async {
    setState(() => loading = true);
    try {
      // Load receiver's profile (the profile being viewed)
      final receiverData = await Api.getUserByUsername(widget.toUsername);
      setState(() {
        receiverUser = receiverData ?? {};
      });

      // Load sender's profile and balance (current logged-in user)
      if (widget.fromUsername.isNotEmpty) {
        final senderData = await Api.getUserByUsername(widget.fromUsername);
        final bal = await Api.getBalanceByUsername(widget.fromUsername);
        setState(() {
          senderUser = senderData ?? {};
          balance = (bal ?? 0) / 100.0;
        });
      } else {
        // Check if this is a shared link access (not requiring authentication)

        try {
          Uri uri = Uri.base;
          if (uri.fragment.isNotEmpty && uri.fragment.startsWith('/')) {
            uri = Uri.parse(uri.fragment);
          }
          isSharedLink = uri.path == '/social-profile' &&
              uri.queryParameters['username'] != null;
        } catch (_) {}

        // Only show login modal if NOT a shared link access
        if (!isSharedLink) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _showLoginModal();
          });
        }
      }

      // Handle profile picture for the receiver (profile being viewed)
      final receiverPicUrl = receiverUser['profile_picture']?.toString();
      if (receiverPicUrl != null && receiverPicUrl.isNotEmpty) {
        if (receiverPicUrl.startsWith('data:image')) {
          try {
            final base64Data = receiverPicUrl.split(',')[1];
            final bytes = base64Decode(base64Data);
            setState(() => receiverImageBytes = bytes);
          } catch (e) {
            debugPrint('Error decoding receiver profile picture: $e');
          }
        } else if (!receiverPicUrl.startsWith('http')) {
          final fullUrl = '${Api.base}${receiverPicUrl}';
          try {
            final pictureRes = await http.get(Uri.parse(fullUrl));
            if (pictureRes.statusCode == 200) {
              setState(() => receiverImageBytes = pictureRes.bodyBytes);
            }
          } catch (e) {
            debugPrint('Error loading receiver profile picture: $e');
          }
        }
      }

      // Handle profile picture for the sender (current user) - only if authenticated
      if (widget.fromUsername.isNotEmpty) {
        final senderPicUrl = senderUser['profile_picture']?.toString();
        if (senderPicUrl != null && senderPicUrl.isNotEmpty) {
          if (senderPicUrl.startsWith('data:image')) {
            try {
              final base64Data = senderPicUrl.split(',')[1];
              final bytes = base64Decode(base64Data);
              setState(() => senderImageBytes = bytes);
            } catch (e) {
              debugPrint('Error decoding sender profile picture: $e');
            }
          } else if (!senderPicUrl.startsWith('http')) {
            final fullUrl = '${Api.base}${senderPicUrl}';
            try {
              final pictureRes = await http.get(Uri.parse(fullUrl));
              if (pictureRes.statusCode == 200) {
                setState(() => senderImageBytes = pictureRes.bodyBytes);
              }
            } catch (e) {
              debugPrint('Error loading sender profile picture: $e');
            }
          }
        }
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error: $e');
    } finally {
      setState(() => loading = false);
    }
  }

  Future<void> _loadDonationHistory() async {
    try {
      final response = await http.get(Uri.parse(
          "${Api.baseUrl}/api/users/${widget.toUsername}/donations30days"));
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          givers30Days = List<Map<String, dynamic>>.from(data['givers'] ?? []);
          totalReceived30Days = ((data['total'] ?? 0) as int) / 100.0;
        });
      }
    } catch (_) {}
  }

  Future<void> _loadFavorites() async {
    try {
      final response = await http.get(Uri.parse(
          "${Api.baseUrl}/api/users/${widget.fromUsername}/favorites"));
      if (response.statusCode == 200) {
        setState(() {
          favoriteGivers =
              List<Map<String, dynamic>>.from(json.decode(response.body));
        });
      }
    } catch (_) {}
  }

  Future<void> _donate(double amount) async {
    if (amount <= 0) return;
    if (amount > balance) {
      Fluttertoast.showToast(
          msg: "Insufficient balance. Please add funds first.");
      return;
    }

    try {
      // Use new balance-based donation API
      final amountCents = (amount * 100).toInt();

      final result = await Api.donateFromBalance(
        fromUsername: widget.fromUsername,
        toUsername: widget.toUsername,
        amountCents: amountCents,
        feeCents: 0, // No fees on donations
      );

      if (result?['error'] != null) {
        final error = result!['error'] as String;
        if (error.contains('Insufficient balance')) {
          Fluttertoast.showToast(
              msg: "Insufficient balance. Please add funds first.");
        } else if (error.contains('not found')) {
          Fluttertoast.showToast(
              msg: "User not found. Please check the username.");
        } else {
          Fluttertoast.showToast(msg: "Donation Error: $error");
        }
        return;
      }

      // Success!
      final newBalances = result?['newBalances'] as Map<String, dynamic>?;
      final donorBalance = newBalances?['donor'] ?? 0;

      Fluttertoast.showToast(
          msg: "✅ Amount of \$${amount.toStringAsFixed(2)} sent successfully!");

      setState(() {
        balance = donorBalance / 100.0; // Update local balance
        showShareIcons = true;
      });

      _load(); // Refresh full data
      _loadDonationHistory();
    } catch (e) {
      Fluttertoast.showToast(msg: "Error: $e");
    }
  }

  Future<void> _navigateToCashOut() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CashOutScreen(username: widget.fromUsername),
      ),
    );

    if (result == true) {
      // Refresh balance after cash out
      _load();
    }
  }

  void _copyProfileHandle() {
    // Strip #$ prefix for cleaner sharing URLs
    final cleanUsername = widget.toUsername.trim().startsWith('#\$')
        ? widget.toUsername.substring(2) // Remove #$ prefix
        : widget.toUsername;
    final profileUrl =
        "https://www.hashtagdollars.com/social-profile?username=${Uri.encodeComponent(cleanUsername)}";

    html.window.navigator.clipboard?.writeText(profileUrl);
    Fluttertoast.showToast(msg: "Profile link copied!");
  }

  void _addToFavorites(Map<String, dynamic> giver) async {
    try {
      final response = await http.post(
        Uri.parse("${Api.baseUrl}/api/users/${widget.fromUsername}/favorites"),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode({"giverUsername": giver['username']}),
      );
      if (response.statusCode == 200) {
        Fluttertoast.showToast(msg: "Added to favorites!");
        _loadFavorites();
      }
    } catch (_) {}
  }

  Widget _socialShareRow() {
    // Strip #$ prefix for cleaner sharing URLs
    final cleanUsername = widget.toUsername.startsWith('#\$')
        ? widget.toUsername.substring(2) // Remove #$ prefix
        : widget.toUsername;
    final displayUsername = widget.toUsername.startsWith('#\$')
        ? widget.toUsername
        : "#\${widget.toUsername}";
    final profileUrl =
        "https://www.hashtagdollars.com/social-profile?username=${Uri.encodeComponent(cleanUsername)}";
    final msg =
        "Check out $displayUsername's profile and support them with micro donations! 👉 $profileUrl";
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        IconButton(
            icon: const Icon(Icons.facebook,
                color: const Color.fromARGB(255, 0, 82, 169)),
            onPressed: () {
              html.window.open(
                  "https://www.facebook.com/sharer/sharer.php?u=${Uri.encodeComponent(profileUrl)}&quote=${Uri.encodeComponent('Support $displayUsername with micro donations!')}",
                  "_blank");
            }),
        IconButton(
            icon: const Icon(Icons.share, color: Colors.lightBlue),
            onPressed: () {
              html.window.open(
                  "https://twitter.com/intent/tweet?text=${Uri.encodeComponent(msg)}",
                  "_blank");
            }),
        IconButton(
            icon: const Icon(Icons.link, color: Colors.white),
            onPressed: () {
              html.window.navigator.clipboard?.writeText(profileUrl);
              Fluttertoast.showToast(msg: "Profile link copied!");
            }),
      ],
    );
  }

  Widget _giverReceiverRow() {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          const Text(
            'Flow',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Giver (Current User)
              Expanded(
                child: Column(
                  children: [
                    CircleAvatar(
                      radius: 30,
                      backgroundColor: Colors.white,
                      backgroundImage: senderImageBytes != null
                          ? MemoryImage(senderImageBytes!)
                          : null,
                      child: senderImageBytes == null
                          ? const Icon(Icons.person,
                              color: const Color.fromARGB(255, 0, 82, 169),
                              size: 30)
                          : null,
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: const Color.fromARGB(255, 0, 82, 169)
                            .withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                            color: const Color.fromARGB(255, 0, 82, 169)),
                      ),
                      child: const Text(
                        'GIVER',
                        style: TextStyle(
                          color: const Color.fromARGB(255, 0, 82, 169),
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.fromUsername,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              // Arrow
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: const Icon(
                  Icons.arrow_forward,
                  color: Colors.white,
                  size: 24,
                ),
              ),

              // Receiver (Profile Owner)
              Expanded(
                child: Column(
                  children: [
                    CircleAvatar(
                      radius: 30,
                      backgroundColor: Colors.white,
                      backgroundImage: receiverImageBytes != null
                          ? MemoryImage(receiverImageBytes!)
                          : null,
                      child: receiverImageBytes == null
                          ? const Icon(Icons.person,
                              color: Colors.white, size: 30)
                          : null,
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.white),
                      ),
                      child: const Text(
                        'RECEIVER',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.toUsername,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _navigateToEditProfile() {
    Navigator.pushNamed(context, '/userprofile',
        arguments: widget.fromUsername);
  }

  void _showShareBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.cardBackground,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => EnhancedShareWidget(
        contentId: widget.toUsername,
        contentText: receiverUser['bio'] ??
            receiverUser['display_name'] ??
            'Check out this profile',
        contentType: 'profile',
      ),
    );
  }

  Widget _buildProfileSection(String display, String shortHandle) {
    // Determine if this is accessed via shared link
    isSharedLink = false;
    try {
      Uri uri = Uri.base;
      if (uri.fragment.isNotEmpty && uri.fragment.startsWith('/')) {
        uri = Uri.parse(uri.fragment);
      }
      isSharedLink = uri.path == '/social-profile' &&
          uri.queryParameters['username'] != null;
    } catch (_) {}

    // Left column logic:
    // - If shared link: show the shared user's profile data (receiver)
    // - If navigation: show current authenticated user's profile data (sender)
    // - If not authenticated: show receiver's profile data
    final showReceiverProfile = isSharedLink || widget.fromUsername.isEmpty;
    final profileUser = showReceiverProfile ? receiverUser : senderUser;
    final profileDisplay = profileUser['display_name'] ?? '';
    final profileEmail = profileUser['email'] ?? '';
    final profileBio = profileUser['bio'] ?? '';
    final profilePhone = profileUser['phone'] ?? '';
    final profileUsername = profileUser['username'] ?? '';
    final profileUrl1 = profileUser['url1'] ?? '';
    final profileUrl2 = profileUser['url2'] ?? '';
    final profileUrl3 = profileUser['url3'] ?? '';
    final profileUrl4 = profileUser['url4'] ?? '';
    final profileUrl5 = profileUser['url5'] ?? '';
    final profileHandle = isSharedLink
        ? (widget.toUsername.startsWith('#\$')
            ? widget.toUsername
            : "#\${widget.toUsername}")
        : (widget.fromUsername.startsWith('#\$')
            ? widget.fromUsername
            : "#\${widget.fromUsername}");

    // Get profile image for the displayed user
    Uint8List? profileImageBytes;
    if (showReceiverProfile) {
      profileImageBytes = receiverImageBytes;
    } else {
      profileImageBytes = senderImageBytes;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Profile Picture
          CircleAvatar(
            radius: 50,
            backgroundColor: Colors.white,
            backgroundImage: profileImageBytes != null
                ? MemoryImage(profileImageBytes)
                : null,
            child: profileImageBytes == null
                ? const Icon(Icons.account_circle, color: Colors.grey, size: 50)
                : null,
          ),
          // const SizedBox(height: 16),

          // // Username
          // if (profileUsername.isNotEmpty) ...[
          //   SelectableText(
          //     profileUsername,
          //     style: const TextStyle(
          //       fontSize: 18,
          //       fontWeight: FontWeight.bold,
          //       color: Colors.black87,
          //     ),
          //     textAlign: TextAlign.center,
          //   ),
          //   const SizedBox(height: 8),
          // ],

          // Handle with copy button
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: SelectableText(
                  '$profileHandle',
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 20,
                    color: Color.fromARGB(255, 0, 82, 169),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.copy,
                    color: const Color.fromARGB(255, 0, 82, 169), size: 20),
                onPressed: () => _copyProfileHandle(),
                tooltip: 'Copy handle',
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Display Name
          if (profileDisplay.isNotEmpty) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.person, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text(
                  profileDisplay,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
          ],

          // Email
          if (profileEmail.isNotEmpty) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.email, color: Colors.orange, size: 20),
                const SizedBox(width: 8),
                SelectableText(
                  profileEmail,
                  style: const TextStyle(fontSize: 16),
                ),
              ],
            ),
            const SizedBox(height: 12),
          ],

          // Bio
          if (profileBio.isNotEmpty) ...[
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.info, color: Colors.purple, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    profileBio,
                    style: const TextStyle(fontSize: 16),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
          ],

          // Phone
          if (profilePhone.isNotEmpty) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.phone, color: Colors.teal, size: 20),
                const SizedBox(width: 8),
                SelectableText(
                  profilePhone,
                  style: const TextStyle(fontSize: 16),
                ),
              ],
            ),
            const SizedBox(height: 12),
          ],

          // URLs
          if (profileUrl1.isNotEmpty) ...[
            _buildClickableUrl(profileUrl1, profileUrl1),
            const SizedBox(height: 8),
          ],
          if (profileUrl2.isNotEmpty) ...[
            _buildClickableUrl(profileUrl2, profileUrl2),
            const SizedBox(height: 8),
          ],
          if (profileUrl3.isNotEmpty) ...[
            _buildClickableUrl(profileUrl3, profileUrl3),
            const SizedBox(height: 8),
          ],
          if (profileUrl4.isNotEmpty) ...[
            _buildClickableUrl(profileUrl4, profileUrl4),
            const SizedBox(height: 8),
          ],
          if (profileUrl5.isNotEmpty) ...[
            _buildClickableUrl(profileUrl5, profileUrl5),
          ],
        ],
      ),
    );
  }

  Widget _buildClickableUrl(String label, String url) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(Icons.link,
            color: const Color.fromARGB(255, 0, 82, 169), size: 20),
        const SizedBox(width: 8),
        Expanded(
          child: InkWell(
            onTap: () {
              try {
                html.window.open(
                    url.startsWith('http') ? url : 'https://$url', '_blank');
              } catch (e) {
                Fluttertoast.showToast(msg: 'Could not open link');
              }
            },
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                color: const Color.fromARGB(255, 0, 82, 169),
                decoration: TextDecoration.underline,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ],
    );
  }

  void _copySenderHandle() {
    final handle = widget.fromUsername.startsWith('#\$')
        ? widget.fromUsername
        : "#\${widget.fromUsername}";
    html.window.navigator.clipboard?.writeText(handle);
    Fluttertoast.showToast(msg: "Your handle copied!");
  }

  void _showLoginModal() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Login Required'),
          content: const Text(
            'To interact with profiles and make donations, you need to be logged in. '
            'Please log in to continue.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.pushReplacementNamed(context, '/landing');
              },
              child: const Text('Go to Landing'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.pushReplacementNamed(context, '/login');
              },
              child: const Text('Login'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildActionsSection() {
    // If user is not authenticated, show limited actions
    if (widget.fromUsername.isEmpty) {
      return Column(
        children: [
          // Show receiver info only
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                const Text(
                  'Profile Information',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Receiver (Profile Owner)
                    Expanded(
                      child: Column(
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: Colors.white,
                            backgroundImage: receiverImageBytes != null
                                ? MemoryImage(receiverImageBytes!)
                                : null,
                            child: receiverImageBytes == null
                                ? const Icon(Icons.person,
                                    color: Colors.white, size: 30)
                                : null,
                          ),
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.white),
                            ),
                            child: const Text(
                              'CREATOR',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            widget.toUsername,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Actions Container - Login prompt
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12)),
            child: Column(
              children: [
                const Text(
                  'Want to support this creator?',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                const Text(
                  'Log in to make micro-donations and show your support!',
                  style: TextStyle(color: Colors.white70),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () =>
                      Navigator.pushReplacementNamed(context, '/login'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color.fromARGB(255, 0, 82, 169),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 32, vertical: 12),
                  ),
                  child: const Text('Login to Give',
                      style: TextStyle(fontSize: 16, color: Colors.white)),
                ),
              ],
            ),
          ),
        ],
      );
    }

    return Column(
      children: [
        // Giver/Receiver Display
        _giverReceiverRow(),

        // Actions Container
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12)),
          child: Column(
            children: [
              // Show current balance for donor
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: const Color.fromARGB(255, 0, 82, 169).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'Your Balance: \$${balance.toStringAsFixed(2)}',
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              // Quick donation buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: () => _donate(0.25),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                    ),
                    child: const Text('\$0.25'),
                  ),
                  ElevatedButton(
                    onPressed: () => _donate(0.50),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                    ),
                    child: const Text('\$0.50'),
                  ),
                  ElevatedButton(
                    onPressed: () => _donate(1.00),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                    ),
                    child: const Text('\$1.00'),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              TextField(
                style: AppTextStyles.inputText,
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                decoration: AppInputDecorations.primaryInput(
                  hintText: "Custom Amount (USD)",
                ),
                onChanged: (val) {
                  setState(() {
                    customAmount = double.tryParse(val) ?? 0.25;
                  });
                },
              ),

              const SizedBox(height: 12),

              ElevatedButton(
                style: AppButtonStyles.primaryButton.copyWith(
                  backgroundColor: MaterialStateProperty.all(
                    (customAmount > 0 && customAmount <= balance)
                        ? const Color.fromARGB(255, 0, 82, 169)
                        : Colors.grey,
                  ),
                ),
                onPressed: (customAmount > 0 && customAmount <= balance)
                    ? () => _donate(customAmount)
                    : null,
                child: Text(
                    customAmount > balance
                        ? "Insufficient Balance"
                        : "Give \$${customAmount.toStringAsFixed(2)}",
                    style: TextStyle(fontSize: 16, color: Colors.white)),
              ),

              if (customAmount > balance)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    'Add funds to your account to make donations',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.warningText,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

              const SizedBox(height: 16),

              ElevatedButton(
                style: AppButtonStyles.primaryButton.copyWith(
                  backgroundColor: MaterialStateProperty.all(Colors.orange),
                ),
                onPressed: _navigateToCashOut,
                child: const Text('Cash Out'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final display = receiverUser['display_name'] ?? '';
    final shortHandle = widget.toUsername.startsWith('#\$')
        ? widget.toUsername
        : "#\${widget.toUsername}";
    final isOwnProfile = widget.fromUsername == widget.toUsername;

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            if (!isSharedLink) {
              Navigator.pop(context);
            } else {
              // go back to main url
              Navigator.pushReplacementNamed(context, '/home');
            }
          },
        ),
        title: Text('Social Profile - ${widget.toUsername}'),
        actions: [
          if (isOwnProfile)
            IconButton(
              icon: const Icon(Icons.edit),
              tooltip: 'Edit Profile',
              onPressed: _navigateToEditProfile,
            ),
          IconButton(
            icon: const Icon(Icons.share),
            tooltip: 'Share Profile',
            onPressed: _showShareBottomSheet,
          ),
        ],
      ),
      body: Container(
        color: AppColors.roseGold, // Rose Gold background
        child: Column(
          children: [
            Expanded(
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(
                      maxWidth: 1200), // Wider for two columns
                  child: loading
                      ? const Center(child: CircularProgressIndicator())
                      : Padding(
                          padding: const EdgeInsets.all(16),
                          child: SingleChildScrollView(
                            child: Column(
                              children: [
                                // Logo
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Image.asset('assets/logo.png', height: 50),
                                    // Title
                                    const Text(
                                      'Hashtag Dollars',
                                      style: AppTextStyles.title,
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 10),

                                // Responsive Two Column Layout
                                LayoutBuilder(
                                  builder: (context, constraints) {
                                    // For smaller screens, stack vertically
                                    if (constraints.maxWidth < 900) {
                                      return Column(
                                        children: [
                                          // Profile Section (Top on mobile)
                                          Container(
                                            width: double.infinity,
                                            margin: const EdgeInsets.only(
                                                bottom: 16),
                                            child: _buildProfileSection(
                                                display, shortHandle),
                                          ),
                                          // Actions Section (Bottom on mobile)
                                          Container(
                                            width: double.infinity,
                                            child: _buildActionsSection(),
                                          ),
                                        ],
                                      );
                                    } else {
                                      // For larger screens, side by side
                                      return Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          // Left Column: Profile Data
                                          Expanded(
                                            flex: 1,
                                            child: Container(
                                              margin: const EdgeInsets.only(
                                                  right: 16),
                                              child: _buildProfileSection(
                                                  display, shortHandle),
                                            ),
                                          ),
                                          // Right Column: Actions
                                          Expanded(
                                            flex: 1,
                                            child: Container(
                                              child: _buildActionsSection(),
                                            ),
                                          ),
                                        ],
                                      );
                                    }
                                  },
                                ),

                                const SizedBox(height: 20),
                              ],
                            ),
                          ),
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
