#!/usr/bin/env node

/**
 * 💰 Add Balance to User Script
 * 
 * Usage:
 *   node scripts/add-balance.js <username> <amount>
 *   node scripts/add-balance.js "#$john" 10.50
 * 
 * This script adds balance to a user's account directly.
 * Useful for testing or admin purposes.
 */

require('dotenv').config();
const { db } = require('../db');
const { v4: uuidv4 } = require('uuid');

// Get command line arguments
const args = process.argv.slice(2);

if (args.length !== 2) {
  console.log('❌ Usage: node scripts/add-balance.js <username> <amount>');
  console.log('📝 Example: node scripts/add-balance.js "#$john" 10.50');
  process.exit(1);
}

const [username, amountStr] = args;
const amount = parseFloat(amountStr);

if (isNaN(amount) || amount <= 0) {
  console.log('❌ Amount must be a positive number');
  process.exit(1);
}

const amountCents = Math.floor(amount * 100);

function addBalance() {
  try {
    console.log('🔍 Looking for user:', username);

    // Check if user exists
    const user = db.prepare('SELECT * FROM users WHERE username = ?').get(username);
    
    if (!user) {
      console.log('❌ User not found:', username);
      console.log('💡 Available users:');
      
      const allUsers = db.prepare('SELECT username, balance FROM users ORDER BY created_at DESC LIMIT 10').all();
      allUsers.forEach(u => {
        const bal = (u.balance / 100).toFixed(2);
        console.log(`   - ${u.username} ($${bal})`);
      });
      
      process.exit(1);
    }

    const oldBalance = user.balance / 100;
    const newBalance = (user.balance + amountCents) / 100;

    console.log('✅ User found:', user.username);
    console.log('💰 Current balance:', `$${oldBalance.toFixed(2)}`);
    console.log('➕ Adding:', `$${amount.toFixed(2)}`);
    console.log('💰 New balance will be:', `$${newBalance.toFixed(2)}`);

    // Update balance in database
    console.log('💾 Updating balance...');
    const result = db.prepare('UPDATE users SET balance = balance + ? WHERE username = ?')
      .run(amountCents, username);

    if (result.changes === 0) {
      console.log('❌ Failed to update balance');
      process.exit(1);
    }

    // Create transaction record
    const transactionId = uuidv4();
    db.prepare(`
      INSERT INTO transactions (id, from_user, to_user, amount, type, note, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `).run(
      transactionId,
      null, // from admin
      user.id,
      amountCents,
      'admin_credit',
      `Admin added $${amount.toFixed(2)} to account`,
      Date.now()
    );

    console.log('✅ Balance updated successfully!');
    console.log('📋 Transaction ID:', transactionId);

  } catch (error) {
    console.error('❌ Error adding balance:', error.message);
    process.exit(1);
  }
}

// Run the script
addBalance();