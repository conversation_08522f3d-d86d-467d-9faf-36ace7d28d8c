#!/bin/bash

echo "Updating deployemnet"
git pull origin production
rm -rf ../frontend/
cp -r flutter_app/web-build/ ../frontend/
echo "Deployment completed for frontend"
echo "Reloading nginx"
sudo systemctl reload nginx

#  backend
echo "Deploying backend"
cp -r backend/* ../backend
cd ../backend/
 npm install --production
 npm install nanoid@3

echo "Restarting backend using pm2..."
pm2 logs backend
pm2 reload backend
echo "Backend deployment completed"