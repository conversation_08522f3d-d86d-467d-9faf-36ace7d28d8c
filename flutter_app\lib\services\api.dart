import 'dart:convert';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

// Enhanced authentication service for better session persistence
class AuthService {
  static const String _kToken = 'hf_token';
  static const String _kUserId = 'hf_userId';
  static const String _kUsername = 'hf_username';
  static const String _kLoginTimestamp = 'hf_login_timestamp';
  static const String _kSessionValid = 'hf_session_valid';

  static SharedPreferences? _prefs;

  // Initialize SharedPreferences instance
  static Future<void> _initPrefs() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  // Enhanced save auth with timestamp and validation
  static Future<void> saveAuth(
      String token, String userId, String username) async {
    await _initPrefs();
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    await _prefs!.setString(_kToken, token);
    await _prefs!.setString(_kUserId, userId);
    await _prefs!.setString(_kUsername, username);
    await _prefs!.setInt(_kLoginTimestamp, timestamp);
    await _prefs!.setBool(_kSessionValid, true);

    print(
        '🔐 AuthService: Session saved for user $username at ${DateTime.fromMillisecondsSinceEpoch(timestamp)}');
  }

  // Enhanced clear auth with complete cleanup
  static Future<void> clearAuth() async {
    await _initPrefs();
    await _prefs!.remove(_kToken);
    await _prefs!.remove(_kUserId);
    await _prefs!.remove(_kUsername);
    await _prefs!.remove(_kLoginTimestamp);
    await _prefs!.setBool(_kSessionValid, false);

    // <========== logs were here, add again if required for debug =========>
  }

  // Check if session is still valid (not expired)
  static Future<bool> isSessionValid() async {
    await _initPrefs();
    final isValid = _prefs!.getBool(_kSessionValid) ?? false;
    final timestamp = _prefs!.getInt(_kLoginTimestamp) ?? 0;

    if (!isValid || timestamp == 0) return false;

    // Check if session is older than 30 days
    final sessionAge = DateTime.now().millisecondsSinceEpoch - timestamp;
    final thirtyDaysMs = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds

    if (sessionAge > thirtyDaysMs) {
      // <========== logs were here, add again if required for debug =========>
      await clearAuth();
      return false;
    }

    return true;
  }

  // Enhanced token getter with validation
  static Future<String?> token() async {
    if (!await isSessionValid()) return null;
    await _initPrefs();
    return _prefs!.getString(_kToken);
  }

  // Enhanced userId getter with validation
  static Future<String?> userId() async {
    if (!await isSessionValid()) return null;
    await _initPrefs();
    return _prefs!.getString(_kUserId);
  }

  // Enhanced username getter with validation
  static Future<String?> username() async {
    if (!await isSessionValid()) return null;
    await _initPrefs();
    final username = _prefs!.getString(_kUsername);
    // <========== logs were here, add again if required for debug =========>
    return username;
  }

  // Get session info for debugging
  static Future<Map<String, dynamic>> getSessionInfo() async {
    await _initPrefs();
    final timestamp = _prefs!.getInt(_kLoginTimestamp) ?? 0;
    return {
      'hasToken': _prefs!.containsKey(_kToken),
      'hasUserId': _prefs!.containsKey(_kUserId),
      'hasUsername': _prefs!.containsKey(_kUsername),
      'isValid': await isSessionValid(),
      'loginTime': timestamp > 0
          ? DateTime.fromMillisecondsSinceEpoch(timestamp).toIso8601String()
          : null,
    };
  }
}

class Api {
  // ✅ Backend URL loaded from .env file
  static String base =
      dotenv.env['SERVER_URL'] ?? 'http://localhost:4242'; // fallback default

  // ✅ Get the full base URL
  static String get baseUrl => base;

  static Map<String, String> _jsonHeaders([String? token]) {
    final headers = {'Content-Type': 'application/json'};
    if (token != null) headers['Authorization'] = 'Bearer $token';
    return headers;
  }

  // ================= AUTH =================
  // Use enhanced AuthService for better session persistence
  static Future<void> saveAuth(
      String token, String userId, String username) async {
    await AuthService.saveAuth(token, userId, username);
  }

  static Future<void> clearAuth() async {
    await AuthService.clearAuth();
  }

  static Future<String?> token() async {
    return await AuthService.token();
  }

  static Future<String?> userId() async {
    return await AuthService.userId();
  }

  static Future<String?> username() async {
    return await AuthService.username();
  }

  // Add method to check session validity
  static Future<bool> isSessionValid() async {
    return await AuthService.isSessionValid();
  }

  // Add method to get session info for debugging
  static Future<Map<String, dynamic>> getSessionInfo() async {
    return await AuthService.getSessionInfo();
  }

  static Future<http.Response> register(
      String email, String password, String username,
      [String? country, String? state, String? city]) {
    final body = json.encode({
      'email': email.trim(),
      'password': password,
      'username': username.trim(),
      if (country != null && country.isNotEmpty) 'country': country,
      if (state != null && state.isNotEmpty) 'state': state,
      if (city != null && city.isNotEmpty) 'city': city,
    });
    return http
        .post(Uri.parse('$baseUrl/api/users/register'),
            headers: _jsonHeaders(), body: body)
        .timeout(const Duration(seconds: 30));
  }

  static Future<bool> login(String email, String password) async {
    final body = json.encode({'email': email.trim(), 'password': password});
    final res = await http
        .post(Uri.parse('$baseUrl/auth/login'),
            headers: _jsonHeaders(), body: body)
        .timeout(const Duration(seconds: 30));

    if (res.statusCode == 200) {
      final js = json.decode(res.body);
      final token = js['token'] as String?;
      final user = js['user'];
      final userId =
          user != null && user['id'] != null ? user['id'].toString() : null;
      final uname = user != null && user['username'] != null
          ? user['username'] as String
          : null;
      if (token != null && userId != null && uname != null) {
        await saveAuth(token, userId, uname);
        return true;
      }
    }
    return false;
  }

  static Future<void> logout() async => clearAuth();

  // ================= USERS =================
  static Future<List<dynamic>?> fetchUsers() async {
    // <========== logs were here, add again if required for debug =========>
    // <========== logs were here, add again if required for debug =========>

    try {
      final res = await http.get(Uri.parse('$baseUrl/api/users')).timeout(
          const Duration(seconds: 45)); // Increased timeout for production

      // <========== logs were here, add again if required for debug =========>
      if (res.statusCode == 200) {
        final data = json.decode(res.body) as List<dynamic>;
        // <========== logs were here, add again if required for debug =========>
        return data;
      } else {
        // <========== logs were here, add again if required for debug =========>
        return null;
      }
    } catch (e) {
      // <========== logs were here, add again if required for debug =========>
      return null;
    }
  }

  static Future<Map<String, dynamic>?> getUserByUsername(
      String username) async {
    final enc = Uri.encodeComponent(username);
    // <========== logs were here, add again if required for debug =========>
    // <========== logs were here, add again if required for debug =========>

    try {
      final res = await http.get(Uri.parse('$baseUrl/api/users/$enc')).timeout(
          const Duration(seconds: 45)); // Increased timeout for production

      // <========== logs were here, add again if required for debug =========>
      // <========== logs were here, add again if required for debug =========>

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        // <========== logs were here, add again if required for debug =========>
        return data;
      } else {
        // <========== logs were here, add again if required for debug =========>
        // <========== logs were here, add again if required for debug =========>
        return null;
      }
    } catch (e) {
      // <========== logs were here, add again if required for debug =========>
      if (e.toString().contains('TimeoutException')) {
        print(
            '⏰ API: Request timed out. This might be due to slow network or server issues.');
      }
      return null;
    }
  }

  // ---------------- Get Balance by Username ----------------
  static Future<int?> getBalanceByUsername(String username) async {
    // <========== logs were here, add again if required for debug =========>
    // <========== logs were here, add again if required for debug =========>

    try {
      final res = await http
          .post(
            Uri.parse('$baseUrl/api/users/balance'),
            headers: {
              'Content-Type': 'application/json',
            },
            body: jsonEncode({
              'username': username, // ✅ send username directly
            }),
          )
          .timeout(
              const Duration(seconds: 45)); // Increased timeout for production

      print("📡 API: Response status=${res.statusCode}");

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        final balance = data['balance'] as int?;
        print("✅ API: Balance for ${data['username']} = $balance");
        return balance;
      } else {
        print("❌ API: Failed to fetch balance. Status: ${res.statusCode}");
        print("❌ API: Error response: ${res.body}");
        return null;
      }
    } catch (e) {
      // <========== logs were here, add again if required for debug =========>
      if (e.toString().contains('TimeoutException')) {
        // <========== logs were here, add again if required for debug =========>
      }
      return null;
    }
  }

  // ---------------- Get Payment History ----------------
  static Future<List<dynamic>?> getPaymentHistory(String username) async {
    // <========== logs were here, add again if required for debug =========>
    print(
        '🔗 API: URL: $baseUrl/api/users/history/${Uri.encodeComponent(username)}');

    try {
      // Encode username for URL safely (#$ -> %23%24)
      final encUsername = Uri.encodeComponent(username);

      final res = await http.get(
        Uri.parse('$baseUrl/api/users/history/$encUsername'),
        headers: {
          'Content-Type': 'application/json',
        },
      ).timeout(
          const Duration(seconds: 45)); // Increased timeout for production

      print("📡 API: Response status=${res.statusCode}");

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        final payments = data['payments'] as List<dynamic>?;
        print(
            '✅ API: Successfully fetched ${payments?.length ?? 0} payment records');
        return payments;
      } else {
        print(
            "❌ API: Failed to fetch payment history. Status: ${res.statusCode}");
        print("❌ API: Error response: ${res.body}");
        return null;
      }
    } catch (e) {
      // <========== logs were here, add again if required for debug =========>
      if (e.toString().contains('TimeoutException')) {
        // <========== logs were here, add again if required for debug =========>
      }
      return null;
    }
  }

  // ================= PAYMENTS =================
  static Future<Map<String, dynamic>?> startTopup({
    required String username,
    required int amountCents,
    String? successUrl,
    String? cancelUrl,
  }) async {
    print(
        '💳 API: Starting topup for username=$username, amount=$amountCents cents');
    // <========== logs were here, add again if required for debug =========>

    final frontendBaseUrl = kIsWeb
        ? '${Uri.base.origin}' // ✅ full URL path for web
        : 'hashtagdollars://payment-success';
    
    final mobileSuccessUrl = 'hashtagdollars://payment-success';
    final mobileCancelUrl = 'hashtagdollars://payment-cancel';

    final body = {
      'username': username,
      'amount': amountCents,
      'baseUrl': frontendBaseUrl,
      'successUrl': successUrl ?? (kIsWeb ? frontendBaseUrl : mobileSuccessUrl),
      'cancelUrl': cancelUrl ?? (kIsWeb ? frontendBaseUrl : mobileCancelUrl),
    };

    try {
      final res = await http
          .post(
            Uri.parse('$baseUrl/api/payments/checkout'),
            headers: _jsonHeaders(),
            body: json.encode(body),
          )
          .timeout(
              const Duration(seconds: 45)); // Increased timeout for production

      // <========== logs were here, add again if required for debug =========>

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        // <========== logs were here, add again if required for debug =========>
        return data;
      } else {
        // <========== logs were here, add again if required for debug =========>
        // <========== logs were here, add again if required for debug =========>
        return null;
      }
    } catch (e) {
      // <========== logs were here, add again if required for debug =========>
      if (e.toString().contains('TimeoutException')) {
        // <========== logs were here, add again if required for debug =========>
      }
      return null;
    }
  }

  static Future<int?> getAmountOnSuccess(String sessionId) async {
    try {
      final res = await http
          .get(
            Uri.parse(
                '$baseUrl/api/payments/getamountonsuccess?session_id=$sessionId'),
            headers: _jsonHeaders(),
          )
          .timeout(const Duration(seconds: 20));

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        return data['amount'] as int?;
      }
    } catch (e) {
      debugPrint('Error fetching amount from session: $e');
    }
    return null;
  }

  // ================= NEW DONATION API =================
  /// Donate using existing balance (no new payment required)
  static Future<Map<String, dynamic>?> donateFromBalance({
    required String fromUsername,
    required String toUsername,
    required int amountCents,
    int feeCents = 0,
  }) async {
    print(
        '💝 API: Donating from balance - $fromUsername -> $toUsername, amount: $amountCents cents');
    // <========== logs were here, add again if required for debug =========>

    try {
      final res = await http
          .post(
            Uri.parse('$baseUrl/api/users/donate'),
            headers: _jsonHeaders(),
            body: json.encode({
              'fromUsername': fromUsername,
              'toUsername': toUsername,
              'amountCents': amountCents,
              'feeCents': feeCents,
            }),
          )
          .timeout(const Duration(seconds: 30));

      // <========== logs were here, add again if required for debug =========>

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        // <========== logs were here, add again if required for debug =========>
        return data;
      } else {
        // <========== logs were here, add again if required for debug =========>
        // <========== logs were here, add again if required for debug =========>
        final errorData = json.decode(res.body) as Map<String, dynamic>;
        return {'error': errorData['error'] ?? 'Donation failed'};
      }
    } catch (e) {
      // <========== logs were here, add again if required for debug =========>
      return {'error': e.toString()};
    }
  }

  /// Get donation history for a user
  static Future<Map<String, dynamic>?> getDonationHistory(
      String username) async {
    // <========== logs were here, add again if required for debug =========>
    // <========== logs were here, add again if required for debug =========>

    try {
      final res = await http
          .post(
            Uri.parse('$baseUrl/api/users/my-donations'),
            headers: _jsonHeaders(),
            body: json.encode({'username': username}),
          )
          .timeout(const Duration(seconds: 30));

      // <========== logs were here, add again if required for debug =========>

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        // <========== logs were here, add again if required for debug =========>
        return data;
      } else {
        print(
            '❌ API: Failed to fetch donation history. Status: ${res.statusCode}');
        return null;
      }
    } catch (e) {
      // <========== logs were here, add again if required for debug =========>
      return null;
    }
  }

  // ================= STRIPE CONNECT API =================
  /// Check if user has Stripe Connect account and verification status
  static Future<Map<String, dynamic>?> getStripeAccountStatus(
      String username) async {
    // <========== logs were here, add again if required for debug =========>
    // <========== logs were here, add again if required for debug =========>

    try {
      final res = await http
          .post(
            Uri.parse('$baseUrl/api/stripe-connect/account-status'),
            headers: _jsonHeaders(),
            body: json.encode({'username': username}),
          )
          .timeout(const Duration(seconds: 30));

      // <========== logs were here, add again if required for debug =========>

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        // <========== logs were here, add again if required for debug =========>
        return data;
      } else {
        print(
            '❌ API: Failed to fetch Stripe account status. Status: ${res.statusCode}');
        return null;
      }
    } catch (e) {
      // <========== logs were here, add again if required for debug =========>
      return null;
    }
  }

  /// Create Stripe Connect onboarding link
  static Future<Map<String, dynamic>?> createStripeOnboardingLink({
    required String username,
    String? refreshUrl,
    String? returnUrl,
  }) async {
    // <========== logs were here, add again if required for debug =========>
    // <========== logs were here, add again if required for debug =========>

    // Always use web URLs for Stripe (required by Stripe)
    final frontendBaseUrl = kIsWeb 
        ? '${Uri.base.origin}' 
        : 'https://www.hashtagdollars.com';
    
    final refreshUrl = '$frontendBaseUrl/stripe-refresh';
    final returnUrl = '$frontendBaseUrl/stripe-return';

    try {
      final res = await http
          .post(
            Uri.parse('$baseUrl/api/stripe-connect/create-account-link'),
            headers: _jsonHeaders(),
            body: json.encode({
              'username': username,
              'refreshUrl': refreshUrl ?? refreshUrl,
              'returnUrl': returnUrl ?? returnUrl,
            }),
          )
          .timeout(const Duration(seconds: 30));

      // <========== logs were here, add again if required for debug =========>

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        // <========== logs were here, add again if required for debug =========>
        return data;
      } else {
        print(
            '❌ API: Failed to create onboarding link. Status: ${res.statusCode}');
        return null;
      }
    } catch (e) {
      // <========== logs were here, add again if required for debug =========>
      return null;
    }
  }

  /// Cash out with real Stripe Connect transfer
  static Future<Map<String, dynamic>?> cashOut({
    required String username,
    required int amountCents,
  }) async {
    print(
        '💰 API: Initiating cash out for username=$username, amount=$amountCents cents');
    // <========== logs were here, add again if required for debug =========>

    try {
      final res = await http
          .post(
            Uri.parse('$baseUrl/api/users/cashout'),
            headers: _jsonHeaders(),
            body: json.encode({
              'username': username,
              'amountCents': amountCents,
            }),
          )
          .timeout(const Duration(seconds: 30));

      // <========== logs were here, add again if required for debug =========>

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        // <========== logs were here, add again if required for debug =========>
        return data;
      } else {
        // <========== logs were here, add again if required for debug =========>
        final errorData = json.decode(res.body) as Map<String, dynamic>;
        return {'error': errorData['error'] ?? 'Cash out failed'};
      }
    } catch (e) {
      // <========== logs were here, add again if required for debug =========>
      return {'error': e.toString()};
    }
  }

  /// Get payout history for a user
  static Future<Map<String, dynamic>?> getPayoutHistory(String username) async {
    // <========== logs were here, add again if required for debug =========>
    // <========== logs were here, add again if required for debug =========>

    try {
      final res = await http
          .post(
            Uri.parse('$baseUrl/api/users/payout-history'),
            headers: _jsonHeaders(),
            body: json.encode({'username': username}),
          )
          .timeout(const Duration(seconds: 30));

      // <========== logs were here, add again if required for debug =========>

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        // <========== logs were here, add again if required for debug =========>
        return data;
      } else {
        print(
            '❌ API: Failed to fetch payout history. Status: ${res.statusCode}');
        return null;
      }
    } catch (e) {
      // <========== logs were here, add again if required for debug =========>
      return null;
    }
  }

  // ================= PASSWORD RESET =================
  static Future<Map<String, dynamic>?> forgotPassword(String email) async {
    // <========== logs were here, add again if required for debug =========>
    // <========== logs were here, add again if required for debug =========>

    try {
      final res = await http
          .post(
            Uri.parse('$baseUrl/api/users/forgot-password'),
            headers: _jsonHeaders(),
            body: json.encode({'email': email}),
          )
          .timeout(const Duration(seconds: 30));

      // <========== logs were here, add again if required for debug =========>

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        // <========== logs were here, add again if required for debug =========>
        return data;
      } else {
        print(
            '❌ API: Password reset request failed. Status: ${res.statusCode}');
        final errorData = json.decode(res.body) as Map<String, dynamic>;
        return {'error': errorData['error'] ?? 'Request failed'};
      }
    } catch (e) {
      // <========== logs were here, add again if required for debug =========>
      return {'error': e.toString()};
    }
  }
}
