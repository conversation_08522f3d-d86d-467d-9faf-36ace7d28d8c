import 'dart:convert';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

class Api {
  // ✅ Backend URL loaded from .env file
  static String base =
      dotenv.env['SERVER_URL'] ?? 'http://localhost:4242'; // fallback default

  // ✅ Get the full base URL
  static String get baseUrl => base;

  static Map<String, String> _jsonHeaders([String? token]) {
    final headers = {'Content-Type': 'application/json'};
    if (token != null) headers['Authorization'] = 'Bearer $token';
    return headers;
  }

  static const _kToken = 'hf_token';
  static const _kUserId = 'hf_userId';
  static const _kUsername = 'hf_username';

  // ================= AUTH =================
  static Future<void> saveAuth(
      String token, String userId, String username) async {
    final p = await SharedPreferences.getInstance();
    await p.setString(_kToken, token);
    await p.setString(_kUserId, userId);
    await p.setString(_kUsername, username);
  }

  static Future<void> clearAuth() async {
    final p = await SharedPreferences.getInstance();
    await p.remove(_kToken);
    await p.remove(_kUserId);
    await p.remove(_kUsername);
  }

  static Future<String?> token() async {
    final p = await SharedPreferences.getInstance();
    return p.getString(_kToken);
  }

  static Future<String?> userId() async {
    final p = await SharedPreferences.getInstance();
    return p.getString(_kUserId);
  }

  static Future<String?> username() async {
    final p = await SharedPreferences.getInstance();
    final username = p.getString(_kUsername);
    print('🔍 API.username() called, found: $username');
    return username;
  }

  static Future<http.Response> register(
      String email, String password, String username) {
    final body = json.encode({
      'email': email.trim(),
      'password': password,
      'username': username.trim(),
    });
    return http
        .post(Uri.parse('$baseUrl/api/users/register'),
            headers: _jsonHeaders(), body: body)
        .timeout(const Duration(seconds: 30));
  }

  static Future<bool> login(String email, String password) async {
    final body = json.encode({'email': email.trim(), 'password': password});
    final res = await http
        .post(Uri.parse('$baseUrl/auth/login'),
            headers: _jsonHeaders(), body: body)
        .timeout(const Duration(seconds: 30));

    if (res.statusCode == 200) {
      final js = json.decode(res.body);
      final token = js['token'] as String?;
      final user = js['user'];
      final userId =
          user != null && user['id'] != null ? user['id'].toString() : null;
      final uname = user != null && user['username'] != null
          ? user['username'] as String
          : null;
      if (token != null && userId != null && uname != null) {
        await saveAuth(token, userId, uname);
        return true;
      }
    }
    return false;
  }

  static Future<void> logout() async => clearAuth();

  // ================= USERS =================
  static Future<List<dynamic>?> fetchUsers() async {
    print('🔍 API: Fetching users list');
    print('🔗 API: URL: $baseUrl/api/users');

    try {
      final res = await http.get(Uri.parse('$baseUrl/api/users')).timeout(
          const Duration(seconds: 45)); // Increased timeout for production

      print('📡 API: Response status: ${res.statusCode}');
      if (res.statusCode == 200) {
        final data = json.decode(res.body) as List<dynamic>;
        print('✅ API: Successfully fetched ${data.length} users');
        return data;
      } else {
        print('❌ API: Failed to fetch users. Status: ${res.statusCode}');
        return null;
      }
    } catch (e) {
      print('🚨 API: Exception while fetching users: $e');
      return null;
    }
  }

  static Future<Map<String, dynamic>?> getUserByUsername(
      String username) async {
    final enc = Uri.encodeComponent(username);
    print('🔍 API: Fetching user profile for: $username');
    print('🔗 API: URL: $baseUrl/api/users/$enc');

    try {
      final res = await http.get(Uri.parse('$baseUrl/api/users/$enc')).timeout(
          const Duration(seconds: 45)); // Increased timeout for production

      print('📡 API: Response status: ${res.statusCode}');
      print('📦 API: Response body length: ${res.body.length}');

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        print('✅ API: Successfully fetched user profile');
        return data;
      } else {
        print('❌ API: Failed to fetch user profile. Status: ${res.statusCode}');
        print('❌ API: Error response: ${res.body}');
        return null;
      }
    } catch (e) {
      print('🚨 API: Exception while fetching user profile: $e');
      if (e.toString().contains('TimeoutException')) {
        print(
            '⏰ API: Request timed out. This might be due to slow network or server issues.');
      }
      return null;
    }
  }

  // ---------------- Get Balance by Username ----------------
  static Future<int?> getBalanceByUsername(String username) async {
    print('💰 API: Requesting balance for username=$username');
    print('🔗 API: URL: $baseUrl/api/users/balance');

    try {
      final res = await http
          .post(
            Uri.parse('$baseUrl/api/users/balance'),
            headers: {
              'Content-Type': 'application/json',
            },
            body: jsonEncode({
              'username': username, // ✅ send username directly
            }),
          )
          .timeout(
              const Duration(seconds: 45)); // Increased timeout for production

      print("📡 API: Response status=${res.statusCode}");

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        final balance = data['balance'] as int?;
        print("✅ API: Balance for ${data['username']} = $balance");
        return balance;
      } else {
        print("❌ API: Failed to fetch balance. Status: ${res.statusCode}");
        print("❌ API: Error response: ${res.body}");
        return null;
      }
    } catch (e) {
      print('🚨 API: Exception while fetching balance: $e');
      if (e.toString().contains('TimeoutException')) {
        print('⏰ API: Balance request timed out');
      }
      return null;
    }
  }

  // ---------------- Get Payment History ----------------
  static Future<List<dynamic>?> getPaymentHistory(String username) async {
    print('📊 API: Requesting payment history for username=$username');
    print(
        '🔗 API: URL: $baseUrl/api/users/history/${Uri.encodeComponent(username)}');

    try {
      // Encode username for URL safely (#$ -> %23%24)
      final encUsername = Uri.encodeComponent(username);

      final res = await http.get(
        Uri.parse('$baseUrl/api/users/history/$encUsername'),
        headers: {
          'Content-Type': 'application/json',
        },
      ).timeout(
          const Duration(seconds: 45)); // Increased timeout for production

      print("📡 API: Response status=${res.statusCode}");

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        final payments = data['payments'] as List<dynamic>?;
        print(
            '✅ API: Successfully fetched ${payments?.length ?? 0} payment records');
        return payments;
      } else {
        print(
            "❌ API: Failed to fetch payment history. Status: ${res.statusCode}");
        print("❌ API: Error response: ${res.body}");
        return null;
      }
    } catch (e) {
      print('🚨 API: Exception while fetching payment history: $e');
      if (e.toString().contains('TimeoutException')) {
        print('⏰ API: Payment history request timed out');
      }
      return null;
    }
  }

  // ================= PAYMENTS =================
  static Future<Map<String, dynamic>?> startTopup({
    required String username,
    required int amountCents,
    String? successUrl,
    String? cancelUrl,
  }) async {
    print(
        '💳 API: Starting topup for username=$username, amount=$amountCents cents');
    print('🔗 API: URL: $baseUrl/api/payments/checkout');

    final frontendBaseUrl = kIsWeb
        ? '${Uri.base.origin}' // ✅ full URL path for web
        : 'hashtagdollars://payment-success';

    final body = {
      'username': username,
      'amount': amountCents,
      'baseUrl': frontendBaseUrl,
      'successUrl': successUrl ?? frontendBaseUrl,
      'cancelUrl': cancelUrl ?? frontendBaseUrl,
    };

    try {
      final res = await http
          .post(
            Uri.parse('$baseUrl/api/payments/checkout'),
            headers: _jsonHeaders(),
            body: json.encode(body),
          )
          .timeout(
              const Duration(seconds: 45)); // Increased timeout for production

      print('📡 API: Topup response status: ${res.statusCode}');

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        print('✅ API: Topup session created successfully');
        return data;
      } else {
        print('❌ API: Topup failed. Status: ${res.statusCode}');
        print('❌ API: Error response: ${res.body}');
        return null;
      }
    } catch (e) {
      print('🚨 API: Exception during topup: $e');
      if (e.toString().contains('TimeoutException')) {
        print('⏰ API: Topup request timed out');
      }
      return null;
    }
  }

  static Future<int?> getAmountOnSuccess(String sessionId) async {
    try {
      final res = await http
          .get(
            Uri.parse(
                '$baseUrl/api/payments/getamountonsuccess?session_id=$sessionId'),
            headers: _jsonHeaders(),
          )
          .timeout(const Duration(seconds: 20));

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        return data['amount'] as int?;
      }
    } catch (e) {
      debugPrint('Error fetching amount from session: $e');
    }
    return null;
  }

  // ================= NEW DONATION API =================
  /// Donate using existing balance (no new payment required)
  static Future<Map<String, dynamic>?> donateFromBalance({
    required String fromUsername,
    required String toUsername,
    required int amountCents,
    int feeCents = 0,
  }) async {
    print('💝 API: Donating from balance - $fromUsername -> $toUsername, amount: $amountCents cents');
    print('🔗 API: URL: $baseUrl/api/users/donate');

    try {
      final res = await http
          .post(
            Uri.parse('$baseUrl/api/users/donate'),
            headers: _jsonHeaders(),
            body: json.encode({
              'fromUsername': fromUsername,
              'toUsername': toUsername,
              'amountCents': amountCents,
              'feeCents': feeCents,
            }),
          )
          .timeout(const Duration(seconds: 30));

      print('📡 API: Donation response status: ${res.statusCode}');

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        print('✅ API: Donation successful');
        return data;
      } else {
        print('❌ API: Donation failed. Status: ${res.statusCode}');
        print('❌ API: Error response: ${res.body}');
        final errorData = json.decode(res.body) as Map<String, dynamic>;
        return {'error': errorData['error'] ?? 'Donation failed'};
      }
    } catch (e) {
      print('🚨 API: Exception during donation: $e');
      return {'error': e.toString()};
    }
  }

  /// Get donation history for a user
  static Future<Map<String, dynamic>?> getDonationHistory(String username) async {
    print('📊 API: Requesting donation history for username=$username');
    print('🔗 API: URL: $baseUrl/api/users/my-donations');

    try {
      final res = await http
          .post(
            Uri.parse('$baseUrl/api/users/my-donations'),
            headers: _jsonHeaders(),
            body: json.encode({'username': username}),
          )
          .timeout(const Duration(seconds: 30));

      print('📡 API: Response status: ${res.statusCode}');

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        print('✅ API: Successfully fetched donation history');
        return data;
      } else {
        print('❌ API: Failed to fetch donation history. Status: ${res.statusCode}');
        return null;
      }
    } catch (e) {
      print('🚨 API: Exception while fetching donation history: $e');
      return null;
    }
  }

  // ================= STRIPE CONNECT API =================
  /// Check if user has Stripe Connect account and verification status
  static Future<Map<String, dynamic>?> getStripeAccountStatus(String username) async {
    print('🏦 API: Checking Stripe account status for username=$username');
    print('🔗 API: URL: $baseUrl/api/stripe-connect/account-status');

    try {
      final res = await http
          .post(
            Uri.parse('$baseUrl/api/stripe-connect/account-status'),
            headers: _jsonHeaders(),
            body: json.encode({'username': username}),
          )
          .timeout(const Duration(seconds: 30));

      print('📡 API: Response status: ${res.statusCode}');

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        print('✅ API: Successfully fetched Stripe account status');
        return data;
      } else {
        print('❌ API: Failed to fetch Stripe account status. Status: ${res.statusCode}');
        return null;
      }
    } catch (e) {
      print('🚨 API: Exception while fetching Stripe account status: $e');
      return null;
    }
  }

  /// Create Stripe Connect onboarding link
  static Future<Map<String, dynamic>?> createStripeOnboardingLink({
    required String username,
    String? refreshUrl,
    String? returnUrl,
  }) async {
    print('🔗 API: Creating Stripe onboarding link for username=$username');
    print('🔗 API: URL: $baseUrl/api/stripe-connect/create-account-link');

    final frontendBaseUrl = kIsWeb ? '${Uri.base.origin}' : 'hashtagdollars://stripe-return';

    try {
      final res = await http
          .post(
            Uri.parse('$baseUrl/api/stripe-connect/create-account-link'),
            headers: _jsonHeaders(),
            body: json.encode({
              'username': username,
              'refreshUrl': refreshUrl ?? '$frontendBaseUrl/stripe-refresh',
              'returnUrl': returnUrl ?? '$frontendBaseUrl/stripe-return',
            }),
          )
          .timeout(const Duration(seconds: 30));

      print('📡 API: Response status: ${res.statusCode}');

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        print('✅ API: Successfully created onboarding link');
        return data;
      } else {
        print('❌ API: Failed to create onboarding link. Status: ${res.statusCode}');
        return null;
      }
    } catch (e) {
      print('🚨 API: Exception while creating onboarding link: $e');
      return null;
    }
  }

  /// Cash out with real Stripe Connect transfer
  static Future<Map<String, dynamic>?> cashOut({
    required String username,
    required int amountCents,
  }) async {
    print('💰 API: Initiating cash out for username=$username, amount=$amountCents cents');
    print('🔗 API: URL: $baseUrl/api/users/cashout');

    try {
      final res = await http
          .post(
            Uri.parse('$baseUrl/api/users/cashout'),
            headers: _jsonHeaders(),
            body: json.encode({
              'username': username,
              'amountCents': amountCents,
            }),
          )
          .timeout(const Duration(seconds: 30));

      print('📡 API: Response status: ${res.statusCode}');

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        print('✅ API: Cash out initiated successfully');
        return data;
      } else {
        print('❌ API: Cash out failed. Status: ${res.statusCode}');
        final errorData = json.decode(res.body) as Map<String, dynamic>;
        return {'error': errorData['error'] ?? 'Cash out failed'};
      }
    } catch (e) {
      print('🚨 API: Exception during cash out: $e');
      return {'error': e.toString()};
    }
  }

  /// Get payout history for a user
  static Future<Map<String, dynamic>?> getPayoutHistory(String username) async {
    print('📊 API: Requesting payout history for username=$username');
    print('🔗 API: URL: $baseUrl/api/users/payout-history');

    try {
      final res = await http
          .post(
            Uri.parse('$baseUrl/api/users/payout-history'),
            headers: _jsonHeaders(),
            body: json.encode({'username': username}),
          )
          .timeout(const Duration(seconds: 30));

      print('📡 API: Response status: ${res.statusCode}');

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        print('✅ API: Successfully fetched payout history');
        return data;
      } else {
        print('❌ API: Failed to fetch payout history. Status: ${res.statusCode}');
        return null;
      }
    } catch (e) {
      print('🚨 API: Exception while fetching payout history: $e');
      return null;
    }
  }
}
