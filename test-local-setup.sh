#!/bin/bash

# 🧪 Local Test Environment Verification Script

echo "🧪 Testing Hashtag Dollar Local Setup..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

# Test 1: Check if backend server is running
print_status "Checking if backend server is running..."
if curl -s http://localhost:4242/api/users > /dev/null; then
    print_success "Backend server is running on port 4242"
else
    print_error "Backend server is not running. Start it with: cd backend && npm start"
    exit 1
fi

# Test 2: Check environment files
print_status "Checking environment configuration..."

if [ -f "backend/.env" ]; then
    if grep -q "sk_test_" backend/.env; then
        print_success "Backend using test Stripe keys"
    else
        print_warning "Backend might be using live Stripe keys"
    fi
else
    print_error "Backend .env file not found"
fi

if [ -f "flutter_app/.env" ]; then
    if grep -q "localhost:4242" flutter_app/.env; then
        print_success "Flutter app configured for local backend"
    else
        print_warning "Flutter app might be pointing to remote backend"
    fi
else
    print_error "Flutter .env file not found"
fi

# Test 3: Test API endpoints
print_status "Testing API endpoints..."

# Test user list endpoint
if curl -s http://localhost:4242/api/users | grep -q '\['; then
    print_success "Users API endpoint working"
else
    print_error "Users API endpoint failed"
fi

# Test balance endpoint
if curl -s -X POST http://localhost:4242/api/users/balance \
    -H "Content-Type: application/json" \
    -d '{"username":"#$testuser"}' | grep -q 'balance'; then
    print_success "Balance API endpoint working"
else
    print_warning "Balance API endpoint returned unexpected response (might be normal if no test users exist)"
fi

# Test 4: Check database
print_status "Checking database..."

if [ -f "backend/hashtagdollar.db" ]; then
    user_count=$(sqlite3 backend/hashtagdollar.db "SELECT COUNT(*) FROM users;" 2>/dev/null || echo "0")
    print_success "Database exists with $user_count users"
else
    print_error "Database not found. Run: cd backend && node migrate-database.js"
fi

# Test 5: Check Stripe configuration
print_status "Checking Stripe configuration..."

if grep -q "sk_test_" backend/.env && grep -q "pk_test_" backend/.env; then
    print_success "Stripe test keys configured"
else
    print_error "Stripe test keys not properly configured"
fi

# Test 6: Test donation API
print_status "Testing donation API..."

response=$(curl -s -X POST http://localhost:4242/api/users/donate \
    -H "Content-Type: application/json" \
    -d '{
        "fromUsername": "#$testuser1",
        "toUsername": "#$testuser2",
        "amountCents": 25,
        "feeCents": 0
    }')

if echo "$response" | grep -q "error"; then
    error_msg=$(echo "$response" | grep -o '"error":"[^"]*"' | cut -d'"' -f4)
    if [[ "$error_msg" == *"not found"* ]]; then
        print_warning "Donation API working (test users don't exist yet - normal)"
    else
        print_warning "Donation API returned: $error_msg"
    fi
else
    print_success "Donation API working"
fi

echo ""
echo "🎯 Test Summary:"
echo "✅ Your local test environment is ready!"
echo ""
echo "📋 Next Steps:"
echo "1. Set up Stripe test webhooks (see TEST_ENVIRONMENT_SETUP.md)"
echo "2. Start Flutter app: cd flutter_app && flutter run -d chrome"
echo "3. Test the complete flow with test credit cards"
echo ""
echo "🧪 Test Credit Card: 4242 4242 4242 4242"
echo "📅 Expiry: 12/25, CVC: 123, ZIP: 12345"
echo ""
print_success "Ready to test! 🚀"