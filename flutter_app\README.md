# hashtag_dollar

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.




PROJECT STRUCTURE

hashtag-dollar/
├─ backend/
│  ├─ package.json
│  ├─ server.js
│  ├─ db.js
│  ├─ models.js
│  ├─ routes/
│  │  ├─ auth.js
│  │  ├─ payments.js
│  │  ├─ users.js
│  ├─ web_public/  (simple public donation HTML pages)
│  └─ .env.example
└─ flutter_app/
   ├─ pubspec.yaml
   ├─ lib/
   │  ├─ main.dart
   │  ├─ screens/
   │  │  ├─ login_screen.dart
   │  │  ├─ register_screen.dart
   │  │  ├─ home_screen.dart
   │  │  ├─ profile_screen.dart
   │  │  ├─ topup_screen.dart
   │  ├─ services/api.dart
   │  └─ widgets/
   └─ assets/
      └─ logo.png




STEP A — Backend: install & run
Open PowerShell (or Command Prompt) and go to the project folder:

powershell

cd "C:\WEBSITE BACKUP\FIVERWORK\hashtag-dollar\backend"
Make sure the required backend files exist:

package.json

server.js

db.js

models.js

routes\auth.js

routes\users.js

routes\payments.js

web_public\index.html, web_public\success.html, web_public\cancel.html

.env.example

If any file is missing, paste the contents I gave earlier into that file using a code editor (VS Code / Notepad).

Create a .env from the example and edit it:

powershell
Copy
Edit
copy .env.example .env
notepad .\ .env
Set JWT_SECRET to a random string.

Put your Stripe test keys: STRIPE_SECRET_KEY=sk_test_... and STRIPE_ENDPOINT_SECRET=whsec_... (if you don’t have them yet you can still run most flows).

SERVER_URL should be http://localhost:4242 (or change in Flutter Api.base accordingly).

Install dependencies:

powershell

npm install
Expected: npm finishes without errors.
If you get a JSON parse error or EJSONPARSE:

Ensure package.json is valid JSON. If you previously used npm init -y it should be valid. If corrupted:

powershell

del package.json
npm init -y
# then paste the package.json content I provided into package.json
npm install
If npm install fails for other reasons: paste the npm error log here and I’ll troubleshoot.

Start the backend:

powershell

node server.js



USE flutter run -d web-server to run frontend

flutter build apk --release  =>For creating flutter apk

Always Create newly added table in db.js manually by first using sqlite3, then the command for creating table

RUN sqlite3 hashtagdollar.db under cd "C:\WEBSITE BACKUP\FIVERWORK\hashtag-dollar\backend" to see records of sqlite3 records saved.


HOSTINBG PLATFORM

. Stripe Test Cards

You can use any of these cards in test mode

| Card Number         | Expiry | CVC | Result                  |
| ------------------- | ------ | --- | ----------------------- |
| 4242 4242 4242 4242 | Any    | Any | Successful payment      |
| 4000 0000 0000 9995 | Any    | Any | Declined payment        |
| 4000 0025 0000 3155 | Any    | Any | Requires authentication |
| 4000 0000 0000 0127 | Any    | Any | Card type error         |

TEST USERS DATA.
{
  "email": "<EMAIL>",
  "password": "Password123!",
  "username": "testuser2",
  "display_name": "Test User",
  "full_name": "Test User",
  "card_number": "****************",
  "card_expiry_month": 12,
  "card_expiry_year": 2030,
  "card_cvc": "123"
}



{
  "email": "<EMAIL>",
  "password": "Password123!",
  "username": "testuser",
  "display_name": "Test User",
  "full_name": "Test User",
  "card_number": "****************",
  "card_expiry_month": 12,
  "card_expiry_year": 2030,
  "card_cvc": "123"
}



{
  "email": "<EMAIL>",
  "password": "Password123!",
  "username": "testuser3",
  "display_name": "Test User",
  "full_name": "Test User",
  "card_number": "****************",
  "card_expiry_month": 12,
  "card_expiry_year": 2030,
  "card_cvc": "123"
}


in users table , usernames appear as #$testuser , #$testuser2 