import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class PaymentFailureScreen extends StatelessWidget {
  const PaymentFailureScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Optionally, you can get extra info from query parameters
    final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;

    String reason = args != null && args.containsKey('reason') 
        ? args['reason'] 
        : 'Payment was not completed.';

    Fluttertoast.showToast(msg: "Payment Failed: $reason");

    return Scaffold(
      appBar: AppBar(
        title: const Text("Payment Failed"),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, color: Colors.red, size: 80),
            const SizedBox(height: 20),
            Text(
              reason,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 20),
            ),
            const SizedBox(height: 30),
            ElevatedButton(
              onPressed: () {
                Navigator.pushReplacementNamed(context, '/profile', arguments: {'username': '#DemoUser'});
              },
              child: const Text("Go back to Profile"),
            )
          ],
        ),
      ),
    );
  }
}
