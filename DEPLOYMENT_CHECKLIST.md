# ✅ Deployment Checklist - Hashtag Dollar

## 🎯 **What You Need to Do Right Now**

### **Step 1: Get Your Stripe Keys** 
1. Go to [Stripe Dashboard](https://dashboard.stripe.com)
2. Switch to **Live mode** (toggle in top left)
3. Go to **Developers → API Keys**
4. Copy your **Live Secret Key** (starts with `sk_live_`)
5. Copy your **Live Publishable Key** (starts with `pk_live_`)

### **Step 2: Update Environment Files**

#### **Update `backend/.env`:**
```bash
STRIPE_SECRET_KEY=sk_live_YOUR_ACTUAL_KEY_HERE
STRIPE_PUBLISHABLE_KEY=pk_live_YOUR_ACTUAL_KEY_HERE
STRIPE_WEBHOOK_SECRET=whsec_YOUR_WEBHOOK_SECRET_HERE
STRIPE_CONNECT_WEBHOOK_SECRET=whsec_YOUR_CONNECT_WEBHOOK_SECRET_HERE
PORT=4242
NODE_ENV=production
JWT_SECRET=your-super-secure-random-string-here
FRONTEND_URL=https://your-domain.com
```

#### **Update `flutter_app/.env`:**
```bash
SERVER_URL=https://your-api-domain.com
STRIPE_PUBLISHABLE_KEY=pk_live_YOUR_ACTUAL_KEY_HERE
```

### **Step 3: Set Up Stripe Webhooks**
1. In Stripe Dashboard, go to **Developers → Webhooks**
2. Click **"Add endpoint"**
3. Add these two endpoints:

**Endpoint 1 - Payments:**
- URL: `https://your-api-domain.com/api/payments/webhook`
- Events: `checkout.session.completed`

**Endpoint 2 - Connect:**
- URL: `https://your-api-domain.com/api/stripe-connect/webhook`  
- Events: `account.updated`, `transfer.created`, `transfer.paid`, `transfer.failed`

4. Copy the webhook secrets to your `.env` file

### **Step 4: Choose Your Deployment Method**

#### **Option A: Quick Cloud Deployment (Recommended)**

**For Backend (Railway/Render):**
1. Push your code to GitHub
2. Connect GitHub repo to Railway or Render
3. Set environment variables in platform dashboard
4. Deploy!

**For Frontend (Netlify/Vercel):**
1. Run: `cd flutter_app && flutter build web`
2. Upload `flutter_app/build/web` folder to Netlify/Vercel
3. Done!

#### **Option B: VPS/Server Deployment**

**Backend:**
```bash
# On your server
git clone your-repo
cd hashtag-dollar/backend
npm install
node migrate-database.js
npm start
```

**Frontend:**
```bash
cd flutter_app
flutter build web
# Upload build/web/* to your web server
```

### **Step 5: Test Everything**
1. **Register a new user** → Should create Stripe account
2. **Add $5 to account** → Should work with real payment
3. **Make a $0.25 donation** → Should be instant
4. **Complete Stripe verification** → Should enable cash out
5. **Cash out $5** → Should initiate real transfer

---

## 🚨 **Important Notes**

### **Security**
- ⚠️ **Never commit `.env` files** to GitHub
- 🔒 **Use strong JWT secrets** (random 32+ characters)
- 🏦 **Test with small amounts** first ($5-10)

### **Stripe Setup**
- 📋 **Complete your Stripe account** verification first
- 💳 **Enable Connect** in your Stripe settings
- 🔔 **Set up webhook endpoints** before going live

### **Monitoring**
- 📊 **Check logs** regularly for first 24 hours
- 💰 **Monitor donation success rate** (should be >99%)
- 🏦 **Track cash out processing** (1-2 business days)

---

## 🎯 **Quick Start Commands**

### **Test Your Setup:**
```bash
# Test backend
cd backend && node test-implementations.js

# Build Flutter app
cd flutter_app && flutter build web
```

### **Deploy with Docker:**
```bash
# Build and run
docker-compose up -d

# Check logs
docker-compose logs -f
```

### **Deploy with PM2:**
```bash
# Install PM2
npm install -g pm2

# Start app
pm2 start backend/server.js --name hashtag-dollar

# Monitor
pm2 monit
```

---

## 🎉 **You're Almost There!**

Your platform is **production-ready**. Just need to:

1. ✅ **Update environment variables** with real Stripe keys
2. ✅ **Set up webhooks** in Stripe Dashboard  
3. ✅ **Deploy** using your preferred method
4. ✅ **Test** with real money (small amounts)
5. ✅ **Launch!** 🚀

**Need help?** Check the detailed `PRODUCTION_DEPLOYMENT_GUIDE.md` or ask me specific questions!

**Ready to make money moves?** Let's get this deployed! 💰