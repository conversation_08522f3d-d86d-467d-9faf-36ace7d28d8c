# Authentication Flow Design for Hashtag Dollar App

## Current Issues

1. App always starts at login screen, ignoring existing authentication
2. No persistent login state check on app startup
3. Registration doesn't automatically log user in
4. No proper logout functionality
5. Navigation doesn't handle authenticated vs unauthenticated states

## Desired Authentication Flow

### App Startup Flow

```
App Starts
    ↓
Check Authentication State
    ↓
Is User Authenticated?
    ├── YES → Navigate to Home Screen
    └── NO  → Show Login Screen
```

### Login Flow

```
Login Screen
    ↓
User enters credentials
    ↓
Validate credentials via API
    ↓
Success?
    ├── YES → Save auth state → Navigate to Home
    └── NO  → Show error message
```

### Registration Flow

```
Register Screen
    ↓
User fills registration form
    ↓
Submit registration via API
    ↓
Success?
    ├── YES → Save auth state → Navigate to Home
    └── NO  → Show error message
```

### Logout Flow

```
Any Authenticated Screen
    ↓
User clicks Logout
    ↓
Clear authentication state
    ↓
Navigate to Login Screen
```

## Implementation Requirements

### 1. Authentication State Management

- Use SharedPreferences to store:
  - JWT token
  - User ID
  - Username
- Check auth state on app startup
- Provide methods to save/clear auth state

### 2. Route Protection

- Define authenticated routes that require login
- Define public routes that don't require login
- Redirect unauthenticated users to login
- Redirect authenticated users away from login/register

### 3. Navigation Updates

- Login screen: After successful login → Home
- Register screen: After successful registration → Home
- Add logout buttons to authenticated screens
- Handle back navigation properly

### 4. API Integration

- Login endpoint returns user data and token
- Registration endpoint returns user data and token
- All authenticated API calls include JWT token
- Handle token expiration gracefully

## Screen Flow Diagram

```
┌─────────────┐    Login     ┌─────────────┐
│             │ ───────────► │             │
│ Login Screen│              │ Home Screen │
│             │ ◄──────────── │             │
└──────┬──────┘   Logout     └──────┬──────┘
       │                           │
       │ Register                  │
       ▼                           │
┌─────────────┐                    │
│             │                    │
│Register     │ ───────────────────┘
│Screen       │    Auto-login after registration
│             │
└─────────────┘
```

## Key Components to Update

1. **main.dart**: Add authentication state check, update routing
2. **LoginScreen**: Update navigation after successful login
3. **RegisterScreen**: Update navigation after successful registration
4. **API Service**: Ensure proper token handling
5. **AppShell**: Add logout functionality
6. **HomeScreen**: Add logout option

## Security Considerations

1. Store tokens securely using SharedPreferences
2. Validate tokens on each authenticated request
3. Handle token expiration gracefully
4. Clear sensitive data on logout
5. Use HTTPS for all API communications

## Testing Scenarios

1. Fresh app install → Should show login screen
2. Login → Should navigate to home and persist across app restarts
3. Registration → Should navigate to home and persist across app restarts
4. Logout → Should clear auth state and show login screen
5. App restart with valid auth → Should go directly to home
6. App restart with invalid/expired auth → Should show login screen
