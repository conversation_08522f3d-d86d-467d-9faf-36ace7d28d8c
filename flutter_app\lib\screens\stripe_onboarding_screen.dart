import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/api.dart';
import '../constants/theme_constants.dart';

class StripeOnboardingScreen extends StatefulWidget {
  final String username;
  
  const StripeOnboardingScreen({
    super.key,
    required this.username,
  });

  @override
  State<StripeOnboardingScreen> createState() => _StripeOnboardingScreenState();
}

class _StripeOnboardingScreenState extends State<StripeOnboardingScreen> {
  bool loading = true;
  Map<String, dynamic>? accountStatus;
  bool isOnboarded = false;
  bool hasAccount = false;

  @override
  void initState() {
    super.initState();
    _checkAccountStatus();
  }

  Future<void> _checkAccountStatus() async {
    setState(() => loading = true);
    
    try {
      final status = await Api.getStripeAccountStatus(widget.username);
      setState(() {
        accountStatus = status;
        hasAccount = status?['hasAccount'] ?? false;
        isOnboarded = status?['onboarded'] ?? false;
      });
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error checking account status: $e');
    } finally {
      setState(() => loading = false);
    }
  }

  Future<void> _startOnboarding() async {
    setState(() => loading = true);
    
    try {
      final result = await Api.createStripeOnboardingLink(
        username: widget.username,
      );
      
      final url = result?['url'] as String?;
      if (url != null) {
        Fluttertoast.showToast(msg: 'Opening Stripe verification...');
        
        final launched = await launchUrl(
          Uri.parse(url),
          mode: LaunchMode.externalApplication,
        );
        
        if (!launched) {
          Fluttertoast.showToast(msg: 'Cannot open verification URL');
        } else {
          // Wait a bit then refresh status
          await Future.delayed(const Duration(seconds: 3));
          _checkAccountStatus();
        }
      } else {
        Fluttertoast.showToast(msg: 'Unable to create onboarding link');
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error starting onboarding: $e');
    } finally {
      setState(() => loading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Stripe Verification'),
      ),
      body: Container(
        color: AppColors.roseGold,
        child: Column(
          children: [
            // Logo
            Image.asset('assets/logo.png', height: 80),
            const SizedBox(height: 16),
            
            Expanded(
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 600),
                  child: loading
                      ? const Center(child: CircularProgressIndicator())
                      : Padding(
                          padding: const EdgeInsets.all(16),
                          child: SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Stripe Verification Status',
                                        style: AppTextStyles.title,
                                      ),
                                      const SizedBox(height: 16),
                                      
                                      _buildStatusRow(
                                        'Account Created',
                                        hasAccount,
                                        hasAccount ? 'Connected to Stripe' : 'Not connected',
                                      ),
                                      
                                      _buildStatusRow(
                                        'Details Submitted',
                                        accountStatus?['detailsSubmitted'] ?? false,
                                        (accountStatus?['detailsSubmitted'] ?? false)
                                            ? 'Information provided'
                                            : 'Verification needed',
                                      ),
                                      
                                      _buildStatusRow(
                                        'Verification Complete',
                                        isOnboarded,
                                        isOnboarded
                                            ? 'Ready for payouts'
                                            : 'Verification pending',
                                      ),
                                    ],
                                  ),
                                ),
                                
                                const SizedBox(height: 24),
                                
                                if (!isOnboarded) ...[
                                  Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: AppColors.warningText.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(color: AppColors.warningText),
                                    ),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            const Icon(Icons.info, color: AppColors.warningText),
                                            const SizedBox(width: 8),
                                            Text(
                                              'Verification Required',
                                              style: AppTextStyles.bodyLarge.copyWith(
                                                color: AppColors.warningText,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 12),
                                        Text(
                                          'To enable cash outs, you need to complete Stripe verification. This process:',
                                          style: AppTextStyles.bodyMedium,
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          '• Takes 2-3 minutes to complete\n'
                                          '• Requires basic personal information\n'
                                          '• Ensures secure money transfers\n'
                                          '• Is required by financial regulations',
                                          style: AppTextStyles.bodyMedium,
                                        ),
                                      ],
                                    ),
                                  ),
                                  
                                  const SizedBox(height: 24),
                                  
                                  SizedBox(
                                    width: double.infinity,
                                    child: ElevatedButton(
                                      style: AppButtonStyles.primaryButton,
                                      onPressed: _startOnboarding,
                                      child: Text(
                                        hasAccount ? 'Continue Verification' : 'Start Verification',
                                      ),
                                    ),
                                  ),
                                ] else ...[
                                  Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: Colors.green.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(color: Colors.green),
                                    ),
                                    child: Column(
                                      children: [
                                        Row(
                                          children: [
                                            const Icon(Icons.check_circle, color: Colors.green),
                                            const SizedBox(width: 8),
                                            Text(
                                              'Verification Complete!',
                                              style: AppTextStyles.bodyLarge.copyWith(
                                                color: Colors.green,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 12),
                                        Text(
                                          'Your account is verified and ready for cash outs. You can now withdraw your earnings to your bank account.',
                                          style: AppTextStyles.bodyMedium,
                                        ),
                                      ],
                                    ),
                                  ),
                                  
                                  const SizedBox(height: 24),
                                  
                                  SizedBox(
                                    width: double.infinity,
                                    child: ElevatedButton(
                                      style: AppButtonStyles.primaryButton.copyWith(
                                        backgroundColor: MaterialStateProperty.all(Colors.green),
                                      ),
                                      onPressed: () => Navigator.pop(context, true),
                                      child: const Text('Continue to Cash Out'),
                                    ),
                                  ),
                                ],
                                
                                const SizedBox(height: 16),
                                
                                SizedBox(
                                  width: double.infinity,
                                  child: ElevatedButton(
                                    style: AppButtonStyles.secondaryButton,
                                    onPressed: _checkAccountStatus,
                                    child: const Text('Refresh Status'),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String title, bool isComplete, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            isComplete ? Icons.check_circle : Icons.radio_button_unchecked,
            color: isComplete ? Colors.green : Colors.grey,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  description,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}