import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../services/api.dart';
import '../constants/theme_constants.dart';
import 'stripe_onboarding_screen.dart';

class CashOutScreen extends StatefulWidget {
  final String username;

  const CashOutScreen({
    super.key,
    required this.username,
  });

  @override
  State<CashOutScreen> createState() => _CashOutScreenState();
}

class _CashOutScreenState extends State<CashOutScreen> {
  bool loading = true;
  int balance = 0;
  List<dynamic> payouts = [];
  Map<String, dynamic>? stripeStatus;
  bool isVerified = false;
  final amountController = TextEditingController();
  double _enteredAmount = 0.0;
  double _platformFee = 0.0;
  double _userReceives = 0.0;

  @override
  void initState() {
    super.initState();
    _loadData();
    amountController.addListener(_calculateFeePreview);
  }

  @override
  void dispose() {
    amountController.removeListener(_calculateFeePreview);
    amountController.dispose();
    super.dispose();
  }

  void _calculateFeePreview() {
    final amountText = amountController.text.trim();
    if (amountText.isEmpty) {
      setState(() {
        _enteredAmount = 0.0;
        _platformFee = 0.0;
        _userReceives = 0.0;
      });
      return;
    }

    try {
      final amount = double.parse(amountText);
      final platformFee = amount * 0.10; // 10% platform fee
      final userReceives = amount - platformFee;

      setState(() {
        _enteredAmount = amount;
        _platformFee = platformFee;
        _userReceives = userReceives;
      });
    } catch (e) {
      setState(() {
        _enteredAmount = 0.0;
        _platformFee = 0.0;
        _userReceives = 0.0;
      });
    }
  }

  Future<void> _loadData() async {
    setState(() => loading = true);

    try {
      // Load balance
      final bal = await Api.getBalanceByUsername(widget.username);

      // Load payout history
      final payoutHistory = await Api.getPayoutHistory(widget.username);

      // Load Stripe verification status
      final stripeAccountStatus =
          await Api.getStripeAccountStatus(widget.username);

      setState(() {
        balance = bal ?? 0;
        payouts = payoutHistory?['payouts'] ?? [];
        stripeStatus = stripeAccountStatus;
        isVerified = stripeAccountStatus?['onboarded'] ?? false;
      });
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error loading data: $e');
    } finally {
      setState(() => loading = false);
    }
  }

  Future<void> _initiateCashOut() async {
    final amountText = amountController.text.trim();
    if (amountText.isEmpty) {
      Fluttertoast.showToast(msg: 'Please enter an amount');
      return;
    }

    final amount = double.tryParse(amountText);
    if (amount == null || amount <= 0) {
      Fluttertoast.showToast(msg: 'Please enter a valid amount');
      return;
    }

    final amountCents = (amount * 100).toInt();

    if (amountCents < 500) {
      Fluttertoast.showToast(msg: 'Minimum cash out is \$5.00');
      return;
    }

    if (amountCents > balance) {
      Fluttertoast.showToast(msg: 'Insufficient balance');
      return;
    }

    if (!isVerified) {
      Fluttertoast.showToast(msg: 'Please complete Stripe verification first');
      return;
    }

    setState(() => loading = true);

    try {
      final result = await Api.cashOut(
        username: widget.username,
        amountCents: amountCents,
      );

      if (result?['error'] != null) {
        final error = result!['error'] as String;
        if (error.contains('verification') || error.contains('onboarding')) {
          // Show verification dialog
          _showVerificationDialog();
        } else {
          Fluttertoast.showToast(msg: 'Cash out failed: $error');
        }
      } else {
        Fluttertoast.showToast(msg: 'Cash out initiated successfully!');
        amountController.clear();
        _loadData(); // Refresh data
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error: $e');
    } finally {
      setState(() => loading = false);
    }
  }

  void _showVerificationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Verification Required'),
        content: const Text(
          'You need to complete Stripe verification before you can cash out. This ensures secure transfers to your bank account.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _navigateToVerification();
            },
            child: const Text('Verify Now'),
          ),
        ],
      ),
    );
  }

  Future<void> _navigateToVerification() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StripeOnboardingScreen(username: widget.username),
      ),
    );

    if (result == true) {
      // Verification completed, refresh data
      _loadData();
    }
  }

  String _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'green';
      case 'processing':
        return 'orange';
      case 'failed':
        return 'red';
      default:
        return 'grey';
    }
  }

  Color _getStatusColorValue(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Colors.green;
      case 'processing':
        return AppColors.warningText;
      case 'failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cash Out'),
      ),
      body: Container(
        color: AppColors.roseGold,
        child: Column(
          children: [
            // Logo
            Image.asset('assets/logo.png', height: 80),
            const SizedBox(height: 16),

            Expanded(
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 600),
                  child: loading
                      ? const Center(child: CircularProgressIndicator())
                      : RefreshIndicator(
                          onRefresh: _loadData,
                          child: SingleChildScrollView(
                            physics: const AlwaysScrollableScrollPhysics(),
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Balance Card
                                Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Available Balance',
                                        style: AppTextStyles.bodyLarge,
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        '\$${(balance / 100).toStringAsFixed(2)}',
                                        style: AppTextStyles.title.copyWith(
                                          fontSize: 32,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                                const SizedBox(height: 24),

                                // Verification Status
                                Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: isVerified
                                        ? AppColors.successText.withOpacity(0.1)
                                        : AppColors.warningText
                                            .withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: isVerified
                                          ? AppColors.successText
                                          : AppColors.warningText,
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(
                                        isVerified
                                            ? Icons.verified
                                            : Icons.warning,
                                        color: isVerified
                                            ? AppColors.successText
                                            : AppColors.warningText,
                                      ),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              isVerified
                                                  ? 'Verification Complete'
                                                  : 'Verification Required',
                                              style: AppTextStyles.bodyMedium
                                                  .copyWith(
                                                fontWeight: FontWeight.bold,
                                                color: isVerified
                                                    ? AppColors.successText
                                                    : AppColors.warningText,
                                              ),
                                            ),
                                            Text(
                                              isVerified
                                                  ? 'Ready for cash outs'
                                                  : 'Complete verification to enable cash outs',
                                              style: AppTextStyles.bodySmall,
                                            ),
                                          ],
                                        ),
                                      ),
                                      if (!isVerified)
                                        ElevatedButton(
                                          onPressed: _navigateToVerification,
                                          child: const Text('Verify'),
                                        ),
                                    ],
                                  ),
                                ),

                                const SizedBox(height: 24),

                                // Cash Out Form
                                Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Cash Out Amount',
                                        style: AppTextStyles.bodyLarge,
                                      ),
                                      const SizedBox(height: 12),
                                      TextField(
                                        controller: amountController,
                                        style: AppTextStyles.inputText,
                                        decoration:
                                            AppInputDecorations.primaryInput(
                                          hintText:
                                              'Enter amount (minimum \$5.00)',
                                          prefixText: '\$ ',
                                        ),
                                        keyboardType: const TextInputType
                                            .numberWithOptions(
                                          decimal: true,
                                        ),
                                        enabled: isVerified && balance >= 500,
                                      ),

                                      // Fee Preview
                                      if (_enteredAmount > 0) ...[
                                        const SizedBox(height: 16),
                                        Container(
                                          width: double.infinity,
                                          padding: const EdgeInsets.all(12),
                                          decoration: BoxDecoration(
                                            color: Colors.blue.withOpacity(0.1),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            border: Border.all(
                                              color:
                                                  Colors.blue.withOpacity(0.3),
                                            ),
                                          ),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                'Cash Out Preview',
                                                style: AppTextStyles.bodyMedium
                                                    .copyWith(
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.blue[700],
                                                ),
                                              ),
                                              const SizedBox(height: 8),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Text(
                                                    'Amount requested:',
                                                    style:
                                                        AppTextStyles.bodySmall,
                                                  ),
                                                  Text(
                                                    '\$${_enteredAmount.toStringAsFixed(2)}',
                                                    style:
                                                        AppTextStyles.bodySmall,
                                                  ),
                                                ],
                                              ),
                                              const SizedBox(height: 4),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Text(
                                                    'Platform fee (10%):',
                                                    style:
                                                        AppTextStyles.bodySmall,
                                                  ),
                                                  Text(
                                                    '-\$${_platformFee.toStringAsFixed(2)}',
                                                    style: AppTextStyles
                                                        .bodySmall
                                                        .copyWith(
                                                      color: Colors.red[600],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              const Divider(height: 16),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Text(
                                                    'You\'ll receive:',
                                                    style: AppTextStyles
                                                        .bodyMedium
                                                        .copyWith(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                  Text(
                                                    '\$${_userReceives.toStringAsFixed(2)}',
                                                    style: AppTextStyles
                                                        .bodyMedium
                                                        .copyWith(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.green[700],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],

                                      const SizedBox(height: 16),
                                      SizedBox(
                                        width: double.infinity,
                                        child: ElevatedButton(
                                          style: AppButtonStyles.primaryButton
                                              .copyWith(
                                            backgroundColor:
                                                MaterialStateProperty.all(
                                              isVerified && balance >= 500
                                                  ? Colors.green
                                                  : Colors.grey,
                                            ),
                                          ),
                                          onPressed:
                                              isVerified && balance >= 500
                                                  ? _initiateCashOut
                                                  : null,
                                          child: Text(
                                            isVerified
                                                ? 'Cash Out'
                                                : 'Verification Required',
                                          ),
                                        ),
                                      ),
                                      if (balance < 500)
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(top: 8),
                                          child: Text(
                                            'Minimum balance of \$5.00 required',
                                            style: AppTextStyles.bodySmall
                                                .copyWith(
                                              color: AppColors.warningText,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),

                                const SizedBox(height: 24),

                                // Payout History
                                Text(
                                  'Payout History',
                                  style: AppTextStyles.title,
                                ),
                                const SizedBox(height: 12),

                                if (payouts.isEmpty)
                                  Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Text(
                                      'No payouts yet',
                                      style: AppTextStyles.bodyMedium,
                                      textAlign: TextAlign.center,
                                    ),
                                  )
                                else
                                  ...payouts
                                      .map((payout) => Container(
                                            margin: const EdgeInsets.only(
                                                bottom: 8),
                                            padding: const EdgeInsets.all(16),
                                            decoration: BoxDecoration(
                                              color:
                                                  Colors.white.withOpacity(0.1),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Row(
                                              children: [
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        '\$${((payout['amountCents'] ?? 0) / 100).toStringAsFixed(2)}',
                                                        style: AppTextStyles
                                                            .bodyLarge
                                                            .copyWith(
                                                          fontWeight:
                                                              FontWeight.bold,
                                                        ),
                                                      ),
                                                      Text(
                                                        DateTime
                                                                .fromMillisecondsSinceEpoch(
                                                          payout['timestamp'] ??
                                                              0,
                                                        )
                                                            .toLocal()
                                                            .toString()
                                                            .split('.')[0],
                                                        style: AppTextStyles
                                                            .bodySmall,
                                                      ),
                                                      if (payout[
                                                              'errorMessage'] !=
                                                          null)
                                                        Text(
                                                          payout[
                                                              'errorMessage'],
                                                          style: AppTextStyles
                                                              .bodySmall
                                                              .copyWith(
                                                            color: Colors.red,
                                                          ),
                                                        ),
                                                    ],
                                                  ),
                                                ),
                                                Container(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                    horizontal: 12,
                                                    vertical: 6,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color: _getStatusColorValue(
                                                        payout['status'] ??
                                                            'pending'),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            16),
                                                  ),
                                                  child: Text(
                                                    (payout['status'] ??
                                                            'pending')
                                                        .toUpperCase(),
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 12,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ))
                                      .toList(),
                              ],
                            ),
                          ),
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
