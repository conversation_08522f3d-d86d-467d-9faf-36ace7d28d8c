# 🔗 Set Up Webhooks Right Now

## 🎯 **What You Need to Do**

You're at the Stripe webhook setup screen. Here's exactly what to do:

### **Step 1: Install ngrok**
```bash
npm install -g ngrok
```

### **Step 2: Start Your Backend (if not running)**
```bash
cd backend
npm start
```
Keep this terminal open.

### **Step 3: Start ngrok (New Terminal)**
```bash
ngrok http 4242
```

You'll see something like:
```
Forwarding    https://abc123.ngrok.io -> http://localhost:4242
```

**Copy that HTTPS URL** (e.g., `https://abc123.ngrok.io`)

### **Step 4: Fill Out Stripe Webhook Form**

**In the Stripe form you have open:**

- **Endpoint URL:** `https://abc123.ngrok.io/api/payments/webhook`
  (Replace `abc123.ngrok.io` with your actual ngrok URL)

- **Description:** `Local testing - payments`

- **Events:** Make sure `checkout.session.completed` is selected

- **Click "Create destination"**

### **Step 5: Copy Webhook Secret**

After creating the webhook:
1. **Click on the webhook** you just created
2. **Copy the "Signing secret"** (starts with `whsec_`)
3. **Update your `backend/.env` file:**

```bash
STRIPE_WEBHOOK_SECRET=whsec_your_actual_secret_here
```

### **Step 6: Create Second Webhook (Connect)**

**Let's find the right Connect events for you:**

1. **First, enable Connect** in your Stripe Dashboard:
   - Go to **Connect** section → Click **"Get started"**
   - Fill out platform info (name: Hashtag Dollar)

2. **Go back** to webhooks list
3. **Click "Add endpoint"** again
4. **Endpoint URL:** `https://abc123.ngrok.io/api/stripe-connect/webhook`
5. **Description:** `Local testing - connect`

6. **Look for these events** (try different names):
   
   **Account Events:**
   - `account.updated` OR `connect.account.updated`
   - `capability.updated`
   - `person.updated`
   
   **Transfer/Payout Events:**
   - `transfer.created` OR `payout.created`
   - `transfer.paid` OR `payout.paid` 
   - `transfer.failed` OR `payout.failed`
   
   **If you can't find exact names, look for:**
   - Anything with "account" in the name
   - Anything with "transfer" or "payout" in the name
   - Search for "connect" in the events list

7. **Select whatever Connect events you can find**
8. **Click "Create destination"**
9. **Copy signing secret:**

```bash
STRIPE_CONNECT_WEBHOOK_SECRET=whsec_your_second_secret_here
```

**Tell me what events you can see and I'll help you pick the right ones!**

### **Step 7: Restart Backend**
```bash
# Stop backend (Ctrl+C)
# Start again
npm start
```

---

## 🧪 **Test It Works**

1. **Start Flutter app:** `cd flutter_app && flutter run -d chrome`
2. **Register a new user**
3. **Add $10** using test card: `4242 4242 4242 4242`
4. **Check backend logs** - should see webhook messages

---

## ⚠️ **Important Notes**

- **Keep ngrok running** - if you close it, the URL changes
- **Free ngrok URLs change** every restart
- **Update webhook URLs** in Stripe if ngrok URL changes

---

## 🎯 **You Need 3 Terminals Running:**

1. **Backend:** `cd backend && npm start`
2. **ngrok:** `ngrok http 4242`
3. **Flutter:** `cd flutter_app && flutter run -d chrome`

**Ready to test!** 🚀