import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../constants/theme_constants.dart';

class ShareBottomSheet extends StatelessWidget {
  final String shareText;
  final String? shareUrl;

  const ShareBottomSheet({
    super.key,
    required this.shareText,
    this.shareUrl,
  });

  static void show(BuildContext context, {
    required String shareText,
    String? shareUrl,
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => ShareBottomSheet(
        shareText: shareText,
        shareUrl: shareUrl,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final fullShareText = shareUrl != null ? '$shareText\n\n$shareUrl' : shareText;

    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Share',
                  style: AppTextStyles.title.copyWith(
                    color: Colors.black,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),
          
          // Share options
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: [
                // Copy to clipboard
                _ShareOption(
                  icon: Icons.copy,
                  title: 'Copy to Clipboard',
                  onTap: () {
                    Clipboard.setData(ClipboardData(text: fullShareText));
                    Fluttertoast.showToast(msg: 'Copied to clipboard!');
                    Navigator.pop(context);
                  },
                ),
                
                const SizedBox(height: 12),
                
                // Share via system
                _ShareOption(
                  icon: Icons.share,
                  title: 'Share via...',
                  onTap: () {
                    Share.share(fullShareText);
                    Navigator.pop(context);
                  },
                ),
                
                const SizedBox(height: 12),
                
                // Text message
                _ShareOption(
                  icon: Icons.message,
                  title: 'Text Message',
                  onTap: () {
                    Share.share(fullShareText, subject: 'Check this out!');
                    Navigator.pop(context);
                  },
                ),
                
                const SizedBox(height: 12),
                
                // Email
                _ShareOption(
                  icon: Icons.email,
                  title: 'Email',
                  onTap: () {
                    Share.share(
                      fullShareText,
                      subject: 'Check out this profile on Hashtag Dollars',
                    );
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
          
          // Preview text
          Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Preview:',
                  style: AppTextStyles.bodySmall.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  fullShareText,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
          
          // Bottom padding for safe area
          SizedBox(height: MediaQuery.of(context).padding.bottom + 20),
        ],
      ),
    );
  }
}

class _ShareOption extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;

  const _ShareOption({
    required this.icon,
    required this.title,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.roseGold.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: AppColors.roseGold,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Text(
              title,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.black87,
              ),
            ),
            const Spacer(),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }
}
