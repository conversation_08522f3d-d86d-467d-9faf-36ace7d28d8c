import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../services/api.dart';
import '../widgets/navigation_bar.dart';
import '../widgets/footer.dart';
import '../constants/theme_constants.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _obscurePassword = true;

  Future<void> _login() async {
    final email = _emailController.text.trim();
    final password = _passwordController.text.trim();

    if (email.isEmpty || password.isEmpty) {
      Fluttertoast.showToast(msg: 'Please fill in all fields');
      return;
    }

    try {
      final success = await Api.login(email, password);
      if (success && mounted) {
        // Get the username from SharedPreferences after successful login
        final username = await Api.username();
        Fluttertoast.showToast(msg: 'Login successful!');
        Navigator.pushReplacementNamed(context, '/home', arguments: username);
      } else {
        Fluttertoast.showToast(msg: 'Invalid email or password');
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'Login failed: $e');
    }
  }

  void _forgotPassword() {
    // TODO: Navigate to Forgot Password screen
    Navigator.pushNamed(context, '/forgot-password');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // extendBodyBehindAppBar: true,
      // appBar: const PreferredSize(
      //   preferredSize: Size.fromHeight(60),
      //   child: AppNavigationBar(),
      // ),
      bottomNavigationBar: const AppFooter(),
      body: Column(
        children: [
          Expanded(
            child: Container(
              width: double.infinity,
              color: AppColors.roseGold, // Rose Gold background
              child: Center(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Container(
                    constraints: const BoxConstraints(maxWidth: 600),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset('assets/logo.png',
                            height: 80), // Slightly larger logo
                        const SizedBox(height: 8),
                        const Text(
                          'Login to Hashtag Dollars',
                          style: AppTextStyles.title, // Enlarged from 22 to 24
                        ),
                        const SizedBox(height: 8),
                        TextField(
                          controller: _emailController,
                          keyboardType: TextInputType.emailAddress,
                          style: AppTextStyles.inputText,
                          decoration: AppInputDecorations.primaryInput(
                            hintText: 'Email',
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextField(
                          controller: _passwordController,
                          obscureText: _obscurePassword,
                          style: AppTextStyles.inputText,
                          decoration: AppInputDecorations.primaryInput(
                            hintText: 'Password',
                            suffixIcon: IconButton(
                              icon: Icon(
                                _obscurePassword
                                    ? Icons.visibility
                                    : Icons.visibility_off,
                                color: AppColors.inputHint,
                              ),
                              onPressed: () {
                                setState(() {
                                  _obscurePassword = !_obscurePassword;
                                });
                              },
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Align(
                          alignment: Alignment.centerRight,
                          child: TextButton.icon(
                            onPressed: _forgotPassword,
                            icon: Icon(Icons.lock_outline,
                                color: AppColors.primaryText, size: 18),
                            label: Text(
                              'Forgot Password?',
                              style: AppTextStyles
                                  .bodySmall, // Enlarged from 14 to 14 (but using consistent styling)
                            ),
                          ),
                        ),

                        ElevatedButton(
                          onPressed: _login,
                          style: AppButtonStyles.primaryButton,
                          child: const Text('Login'),
                        ),
                        GestureDetector(
                          onTap: () {
                            Navigator.pushReplacementNamed(
                                context, '/register');
                          },
                          child: Text(
                            'Don\'t have an account? Register',
                            style: AppTextStyles.bodyMedium.copyWith(
                                decoration: TextDecoration.underline,
                                color: AppColors.buttonText),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
