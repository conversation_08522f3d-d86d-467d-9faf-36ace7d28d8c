#!/usr/bin/env node

/**
 * 👥 List All Users Script
 * 
 * Usage:
 *   node scripts/list-users.js
 * 
 * This script lists all users in the database with their details.
 */

require('dotenv').config();
const { db } = require('../db');

function listUsers() {
  try {
    console.log('👥 Hashtag Dollar Users Database\n');

    // Get all users with their details
    const users = db.prepare(`
      SELECT 
        username, 
        email, 
        balance, 
        stripe_account_id,
        stripe_onboarded,
        created_at
      FROM users 
      ORDER BY created_at DESC
    `).all();

    if (users.length === 0) {
      console.log('📭 No users found in database');
      return;
    }

    console.log(`📊 Total Users: ${users.length}\n`);

    users.forEach((user, index) => {
      const balance = (user.balance / 100).toFixed(2);
      const created = new Date(user.created_at).toLocaleDateString();
      const verified = user.stripe_onboarded ? '✅ Verified' : '⏳ Pending';
      const hasStripe = user.stripe_account_id ? '🏦 Connected' : '❌ No Account';

      console.log(`${index + 1}. ${user.username}`);
      console.log(`   📧 Email: ${user.email}`);
      console.log(`   💰 Balance: $${balance}`);
      console.log(`   🏦 Stripe: ${hasStripe}`);
      console.log(`   ✅ Status: ${verified}`);
      console.log(`   📅 Created: ${created}`);
      console.log('');
    });

    // Summary statistics
    const totalBalance = users.reduce((sum, user) => sum + user.balance, 0) / 100;
    const verifiedUsers = users.filter(user => user.stripe_onboarded).length;
    const stripeConnected = users.filter(user => user.stripe_account_id).length;

    console.log('📈 Summary:');
    console.log(`   💰 Total Platform Balance: $${totalBalance.toFixed(2)}`);
    console.log(`   ✅ Verified Users: ${verifiedUsers}/${users.length}`);
    console.log(`   🏦 Stripe Connected: ${stripeConnected}/${users.length}`);

  } catch (error) {
    console.error('❌ Error listing users:', error.message);
    process.exit(1);
  }
}

// Run the script
listUsers();