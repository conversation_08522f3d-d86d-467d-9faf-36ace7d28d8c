import 'package:flutter/foundation.dart';
import 'api.dart';

class BalanceProvider extends ChangeNotifier {
  double _balance = 0.0;
  bool _isLoading = false;
  String? _currentUsername;

  double get balance => _balance;
  bool get isLoading => _isLoading;

  // Initialize with username
  Future<void> initialize(String username) async {
    _currentUsername = username;
    await refreshBalance();
  }

  // Refresh balance from server
  Future<void> refreshBalance() async {
    if (_currentUsername == null) return;

    _isLoading = true;
    notifyListeners();

    try {
      final bal = await Api.getBalanceByUsername(_currentUsername!);
      _balance = (bal ?? 0) / 100.0;
      debugPrint(
          '🔄 BalanceProvider: Refreshed balance for $_currentUsername = \$${_balance.toStringAsFixed(2)}');
    } catch (e) {
      debugPrint('❌ BalanceProvider: Error refreshing balance: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update balance locally (for immediate UI updates)
  void updateBalance(double newBalance) {
    _balance = newBalance;
    debugPrint(
        '💰 BalanceProvider: Updated balance locally to \$${_balance.toStringAsFixed(2)}');
    notifyListeners();
  }

  // Deduct amount from balance
  void deductBalance(double amount) {
    _balance -= amount;
    debugPrint(
        '➖ BalanceProvider: Deducted \$${amount.toStringAsFixed(2)}, new balance: \$${_balance.toStringAsFixed(2)}');
    notifyListeners();
  }

  // Add amount to balance
  void addBalance(double amount) {
    _balance += amount;
    debugPrint(
        '➕ BalanceProvider: Added \$${amount.toStringAsFixed(2)}, new balance: \$${_balance.toStringAsFixed(2)}');
    notifyListeners();
  }

  // Clear balance (logout)
  void clearBalance() {
    _balance = 0.0;
    _currentUsername = null;
    debugPrint('🧹 BalanceProvider: Cleared balance');
    notifyListeners();
  }
}
