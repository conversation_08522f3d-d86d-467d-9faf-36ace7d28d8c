import 'package:flutter/material.dart';

class AppColors {
  // Rose Gold Background
  static const Color roseGold = Color(0xFFB76E79);

  // Text Colors
  static const Color primaryText = Colors.white;
  static const Color secondaryText = Colors.white70;
  static const Color buttonText = Colors.black;

  // Status Colors (High Contrast)
  static const Color successText = Color(0xFF4CAF50); // Bright green
  static const Color warningText = Color.fromARGB(255, 246, 255, 0); // Yellow
  static const Color errorText = Color(0xFFF44336); // Bright red
  static const Color infoText = Color(0xFF2196F3); // Bright blue

  // Button Colors
  static const Color primaryButton = Colors.white;
  static const Color secondaryButton = Colors.white;

  // Input Field Colors
  static const Color inputFill = Color(0x22FFFFFF); // Semi-transparent white
  static const Color inputBorder = Colors.white70;
  static const Color inputText = Colors.white;
  static const Color inputHint = Colors.white70;

  // Card and Container Colors
  static const Color cardBackground = Color(0xFFEEEEEE);
  static const Color containerBackground = Color(0xFFF5F5F5);
}

class AppTextStyles {
  // Large Titles
  static const TextStyle largeTitle = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: AppColors.primaryText,
  );

  // Medium Titles
  static const TextStyle mediumTitle = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.bold,
    color: AppColors.primaryText,
  );

  // Regular Titles
  static const TextStyle title = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.primaryText,
  );

  // Body Text
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 18,
    color: AppColors.primaryText,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 16,
    color: AppColors.primaryText,
  );

  static const TextStyle bodySmall = TextStyle(
    fontSize: 14,
    color: AppColors.secondaryText,
  );

  // Button Text
  static const TextStyle buttonText = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: AppColors.buttonText,
  );

  // Input Text
  static const TextStyle inputText = TextStyle(
    fontSize: 16,
    color: AppColors.inputText,
  );

  // Hint Text
  static const TextStyle hintText = TextStyle(
    fontSize: 16,
    color: AppColors.inputHint,
  );
}

class AppButtonStyles {
  static ButtonStyle primaryButton = ElevatedButton.styleFrom(
    backgroundColor: AppColors.primaryButton,
    foregroundColor: AppColors.buttonText,
    padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
    textStyle: AppTextStyles.buttonText,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8),
    ),
  );

  static ButtonStyle secondaryButton = ElevatedButton.styleFrom(
    backgroundColor: AppColors.secondaryButton,
    foregroundColor: AppColors.buttonText,
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    textStyle: AppTextStyles.buttonText,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8),
    ),
  );
}

class AppInputDecorations {
  static InputDecoration primaryInput({
    required String hintText,
    String? labelText,
    Widget? suffixIcon,
    String? prefixText,
  }) {
    return InputDecoration(
      hintText: hintText,
      labelText: labelText,
      prefixText: prefixText,
      hintStyle: AppTextStyles.hintText,
      labelStyle: AppTextStyles.inputText,
      prefixStyle: AppTextStyles.inputText,
      filled: true,
      fillColor: AppColors.inputFill,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: AppColors.inputBorder),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: AppColors.inputBorder),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: AppColors.primaryText),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
      suffixIcon: suffixIcon,
    );
  }
}
