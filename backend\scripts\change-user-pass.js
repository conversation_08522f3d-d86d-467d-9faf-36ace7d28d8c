#!/usr/bin/env node

/**
 * 🔐 Change User Password Script
 * 
 * Usage:
 *   node scripts/change-user-pass.js <username> <new-password>
 *   node scripts/change-user-pass.js "#$john" "newpassword123"
 * 
 * This script allows you to change a user's password directly in the database.
 * Useful for admin purposes or password recovery.
 */

require('dotenv').config();
const { db } = require('../db');
const bcrypt = require('bcryptjs');

// Get command line arguments
const args = process.argv.slice(2);

if (args.length < 2) {
  console.log('❌ Usage: node scripts/change-user-pass.js <username-or-email> <new-password>');
  console.log('📝 Examples:');
  console.log('   node scripts/change-user-pass.js "<EMAIL>" "newpassword123"');
  console.log('   node scripts/change-user-pass.js \'#$user02\' "newpassword123"');
  console.log('   node scripts/change-user-pass.js "#\\$user02" "newpassword123"');
  process.exit(1);
}

let [username, newPassword] = args;

// Handle PowerShell escaping issues with #$ characters
if (username === '#' && args.length > 2) {
  // PowerShell split "#$user02" into ["#", "$user02", "password"]
  username = '#$' + args[1].substring(1); // Remove $ and add back #$
  newPassword = args[2];
}

// Also allow finding user by email
let searchByEmail = false;
if (!username.startsWith('#$') && username.includes('@')) {
  searchByEmail = true;
}

async function changeUserPassword() {
  try {
    console.log('🔍 Looking for user:', username);

    // Check if user exists (by username or email)
    let user;
    if (searchByEmail) {
      console.log('🔍 Searching by email:', username);
      user = db.prepare('SELECT * FROM users WHERE email = ?').get(username);
    } else {
      user = db.prepare('SELECT * FROM users WHERE username = ?').get(username);
    }
    
    if (!user) {
      console.log('❌ User not found:', username);
      console.log('💡 Available users:');
      
      const allUsers = db.prepare('SELECT username, email FROM users ORDER BY created_at DESC LIMIT 10').all();
      allUsers.forEach(u => {
        console.log(`   - ${u.username} (${u.email})`);
      });
      
      process.exit(1);
    }

    console.log('✅ User found:', user.username, `(${user.email})`);

    // Validate new password
    if (newPassword.length < 6) {
      console.log('❌ Password must be at least 6 characters long');
      process.exit(1);
    }

    // Hash the new password
    console.log('🔐 Hashing new password...');
    const hashedPassword = bcrypt.hashSync(newPassword, 10);

    // Update password in database
    console.log('💾 Updating password in database...');
    const result = searchByEmail 
      ? db.prepare('UPDATE users SET password = ? WHERE email = ?').run(hashedPassword, username)
      : db.prepare('UPDATE users SET password = ? WHERE username = ?').run(hashedPassword, username);

    if (result.changes === 0) {
      console.log('❌ Failed to update password');
      process.exit(1);
    }

    console.log('✅ Password updated successfully!');
    console.log('📋 Summary:');
    console.log(`   Username: ${username}`);
    console.log(`   Email: ${user.email}`);
    console.log(`   New Password: ${newPassword}`);
    console.log('');
    console.log('🔒 The user can now login with the new password.');

  } catch (error) {
    console.error('❌ Error changing password:', error.message);
    process.exit(1);
  }
}

// Run the script
changeUserPassword();