// backend/routes/payments.js
const express = require("express");
const router = express.Router();
const Payment = require("../models"); // Your Payment model
const { body, validationResult } = require("express-validator");
const Stripe = require("stripe");

// Load Stripe secret key from env
const stripe = Stripe(process.env.STRIPE_SECRET);

// ---------------------
// Create new payment (Stripe + DB)
// ---------------------
router.post(
  "/",
  [
    body("amount").isNumeric().withMessage("Amount must be a number"),
    body("currency").isString().withMessage("Currency must be a string"),
    body("method").isString().withMessage("Payment method is required"),
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) return res.status(400).json({ errors: errors.array() });

    try {
      // Use a dummy user ID since authentication is removed
      const dummyUserId = "public_user";

      // Create Stripe PaymentIntent
      const intent = await stripe.paymentIntents.create({
        amount: Math.round(req.body.amount * 100), // amount in cents
        currency: req.body.currency.toLowerCase(),
        payment_method_types: ["card"],
        metadata: {
          userId: dummyUserId,
          reference: req.body.reference || `REF-${Date.now()}`,
        },
      });

      // Save initial payment record in DB
      const payment = new Payment({
        user: dummyUserId,
        amount: req.body.amount,
        currency: req.body.currency,
        method: req.body.method,
        status: "pending",
        reference: req.body.reference || `REF-${Date.now()}`,
        stripePaymentIntentId: intent.id,
      });

      await payment.save();

      res.status(201).json({
        message: "Payment intent created",
        clientSecret: intent.client_secret,
        payment,
      });
    } catch (err) {
      console.error("Stripe Error:", err);
      res.status(500).json({ message: "Stripe Error", error: err.message });
    }
  }
);

// ---------------------
// Get all payments (public)
// ---------------------
router.get("/", async (req, res) => {
  try {
    const payments = await Payment.find().sort({ createdAt: -1 });
    res.json(payments);
  } catch (err) {
    res.status(500).json({ message: "Server Error" });
  }
});

// ---------------------
// Get single payment by ID
// ---------------------
router.get("/:id", async (req, res) => {
  try {
    const payment = await Payment.findOne({ _id: req.params.id });
    if (!payment) return res.status(404).json({ message: "Payment not found" });
    res.json(payment);
  } catch (err) {
    res.status(500).json({ message: "Server Error" });
  }
});

// ---------------------
// Update payment status (manual override)
// ---------------------
router.put("/:id/status", async (req, res) => {
  try {
    const payment = await Payment.findOne({ _id: req.params.id });
    if (!payment) return res.status(404).json({ message: "Payment not found" });

    payment.status = req.body.status || payment.status;
    await payment.save();

    res.json(payment);
  } catch (err) {
    res.status(500).json({ message: "Server Error" });
  }
});

// ---------------------
// Stripe Checkout Session
// ---------------------
router.post("/create-checkout-session", async (req, res) => {
  try {
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ["card"],
      line_items: [
        {
          price_data: {
            currency: "usd",
            product_data: { name: "Donation" },
            unit_amount: req.body.amountCents,
          },
          quantity: 1,
        },
      ],
      mode: "payment",
      success_url: `${req.headers.origin}/success`,
      cancel_url: `${req.headers.origin}/cancel`,
    });

    res.json({ url: session.url });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Stripe checkout failed" });
  }
});

module.exports = router;
