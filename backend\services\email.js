const nodemailer = require('nodemailer');
require('dotenv').config();

// Create SMTP transporter
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT) || 587,
  secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
});

// Verify transporter configuration (only if credentials are provided)
if (process.env.SMTP_USER && process.env.SMTP_PASS) {
  transporter.verify((error, success) => {
    if (error) {
      console.error('❌ SMTP configuration error:', error.message);
    } else {
      console.log('✅ SMTP server is ready to send emails');
    }
  });
} else {
  console.log('⚠️ SMTP credentials not configured - emails will not be sent');
}

class EmailService {
  static async sendPasswordResetEmail(email, resetToken, resetUrl) {
    const mailOptions = {
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to: email,
      subject: 'Reset Your Hashtag Dollars Password',
      html: this.generatePasswordResetHTML(resetUrl)
    };

    try {
      const info = await transporter.sendMail(mailOptions);
      console.log('✅ Password reset email sent:', info.messageId);
      return { success: true, messageId: info.messageId };
    } catch (error) {
      console.error('❌ Error sending password reset email:', error);
      throw error;
    }
  }

  static generatePasswordResetHTML(resetUrl) {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Password</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #8B5CF6;
            margin-bottom: 10px;
        }
        .title {
            font-size: 24px;
            color: #1f2937;
            margin-bottom: 10px;
        }
        .subtitle {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 30px;
        }
        .button {
            display: inline-block;
            padding: 15px 30px;
            background-color: #8B5CF6;
            color: #ffffff;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            box-shadow: 0 4px 6px rgba(139, 92, 246, 0.2);
        }
        .button:hover {
            background-color: #7C3AED;
        }
        .warning {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .warning-title {
            font-weight: bold;
            color: #92400e;
            margin-bottom: 5px;
        }
        .warning-text {
            color: #78350f;
            font-size: 14px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            font-size: 14px;
            color: #6b7280;
            text-align: center;
        }
        .link {
            color: #8B5CF6;
            text-decoration: none;
        }
        .link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">Hashtag Dollars #\$</div>
            <h1 class="title">Reset Your Password</h1>
            <p class="subtitle">We received a request to reset your password. Click the button below to create a new password.</p>
        </div>

        <div style="text-align: center;">
            <a href="${resetUrl}" class="button">Reset Password</a>
        </div>

        <p style="text-align: center; margin: 20px 0;">
            Or copy and paste this link into your browser:<br>
            <a href="${resetUrl}" class="link">${resetUrl}</a>
        </p>

        <div class="warning">
            <div class="warning-title">Security Notice</div>
            <div class="warning-text">
                This password reset link will expire in 1 hour for your security.
                If you didn't request this password reset, please ignore this email.
            </div>
        </div>

        <div class="footer">
            <p>
                If you're having trouble clicking the button, copy and paste the URL above into your web browser.<br>
                Need help? Contact our support team at <a href="mailto:<EMAIL>" class="link"><EMAIL></a>
            </p>
            <p style="margin-top: 10px; font-size: 12px; color: #9ca3af;">
                © 2025 Hashtag Dollars. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
    `;
  }
}

module.exports = EmailService;