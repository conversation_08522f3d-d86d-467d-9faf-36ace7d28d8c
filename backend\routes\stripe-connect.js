const express = require('express');
const router = express.Router();
const Stripe = require('stripe');
const { findUserByUsername } = require('../db');
const { db } = require('../db');

const stripe = Stripe(process.env.STRIPE_SECRET_KEY);

// ---------------- Create Stripe Connect Account ----------------
router.post('/create-account', async (req, res) => {
  try {
    const { username, email, country = 'US' } = req.body;

    if (!username || !email) {
      return res.status(400).json({ error: 'username and email required' });
    }

    const user = findUserByUsername(username);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if user already has a Stripe account
    if (user.stripe_account_id) {
      return res.status(400).json({ 
        error: 'User already has a Stripe account',
        accountId: user.stripe_account_id 
      });
    }

    // Create Stripe Express account
    const account = await stripe.accounts.create({
      type: 'express',
      country: country,
      email: email,
      capabilities: {
        transfers: { requested: true },
      },
      business_type: 'individual',
    });

    // Update user with Stripe account ID
    db.prepare('UPDATE users SET stripe_account_id = ? WHERE username = ?')
      .run(account.id, username);

    console.log(`✅ Created Stripe Connect account ${account.id} for user ${username}`);

    res.json({
      success: true,
      accountId: account.id,
      username: username
    });

  } catch (err) {
    console.error('Error creating Stripe Connect account:', err);
    res.status(500).json({ error: err.message });
  }
});

// ---------------- Create Account Link for Onboarding ----------------
router.post('/create-account-link', async (req, res) => {
  try {
    const { username, refreshUrl, returnUrl } = req.body;

    if (!username) {
      return res.status(400).json({ error: 'username required' });
    }

    const user = findUserByUsername(username);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // If user doesn't have Stripe account, create one first
    if (!user.stripe_account_id) {
      console.log(`🔧 Creating Stripe Connect account for existing user ${username}`);
      
      const account = await stripe.accounts.create({
        type: 'express',
        country: 'US',
        email: user.email,
        capabilities: {
          transfers: { requested: true },
        },
        business_type: 'individual',
      });

      // Update user with new Stripe account ID
      db.prepare('UPDATE users SET stripe_account_id = ? WHERE username = ?')
        .run(account.id, username);
      
      user.stripe_account_id = account.id;
      console.log(`✅ Created Stripe Connect account ${account.id} for existing user ${username}`);
    }

    // Validate URLs - Stripe requires HTTPS URLs
    const validRefreshUrl = refreshUrl && refreshUrl.startsWith('http') 
      ? refreshUrl 
      : `${process.env.FRONTEND_URL}/stripe-refresh`;
    
    const validReturnUrl = returnUrl && returnUrl.startsWith('http') 
      ? returnUrl 
      : `${process.env.FRONTEND_URL}/stripe-return`;

    // Create account link for onboarding
    const accountLink = await stripe.accountLinks.create({
      account: user.stripe_account_id,
      refresh_url: validRefreshUrl,
      return_url: validReturnUrl,
      type: 'account_onboarding',
    });

    console.log(`✅ Created onboarding link for user ${username}`);

    res.json({
      success: true,
      url: accountLink.url,
      accountId: user.stripe_account_id
    });

  } catch (err) {
    console.error('Error creating account link:', err);
    res.status(500).json({ error: err.message });
  }
});

// ---------------- Check Account Status ----------------
router.post('/account-status', async (req, res) => {
  try {
    const { username } = req.body;

    if (!username) {
      return res.status(400).json({ error: 'username required' });
    }

    const user = findUserByUsername(username);
    if (!user || !user.stripe_account_id) {
      return res.json({
        hasAccount: false,
        onboarded: false,
        detailsSubmitted: false
      });
    }

    // Get account details from Stripe
    const account = await stripe.accounts.retrieve(user.stripe_account_id);

    const onboarded = account.details_submitted && account.charges_enabled && account.payouts_enabled;
    const detailsSubmitted = account.details_submitted;

    // Update local database
    if (onboarded !== user.stripe_onboarded || detailsSubmitted !== user.stripe_details_submitted) {
      db.prepare('UPDATE users SET stripe_onboarded = ?, stripe_details_submitted = ? WHERE username = ?')
        .run(onboarded ? 1 : 0, detailsSubmitted ? 1 : 0, username);
    }

    res.json({
      hasAccount: true,
      onboarded: onboarded,
      detailsSubmitted: detailsSubmitted,
      chargesEnabled: account.charges_enabled,
      payoutsEnabled: account.payouts_enabled,
      accountId: user.stripe_account_id
    });

  } catch (err) {
    console.error('Error checking account status:', err);
    res.status(500).json({ error: err.message });
  }
});

// ---------------- Webhook Handler ----------------
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_CONNECT_WEBHOOK_SECRET);
  } catch (err) {
    console.error('❌ Webhook signature failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  console.log('🔄 Stripe Connect Webhook received:', event.type);

  try {
    switch (event.type) {
      case 'account.updated':
        await handleAccountUpdated(event.data.object);
        break;
      
      case 'transfer.created':
        await handleTransferCreated(event.data.object);
        break;
      
      case 'transfer.paid':
        await handleTransferPaid(event.data.object);
        break;
      
      case 'transfer.failed':
        await handleTransferFailed(event.data.object);
        break;
      
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }
  } catch (err) {
    console.error('Error processing webhook:', err);
    return res.status(500).json({ error: 'Webhook processing failed' });
  }

  res.json({ received: true });
});

// ---------------- Webhook Handlers ----------------
async function handleAccountUpdated(account) {
  try {
    const user = db.prepare('SELECT * FROM users WHERE stripe_account_id = ?').get(account.id);
    if (!user) return;

    const onboarded = account.details_submitted && account.charges_enabled && account.payouts_enabled;
    const detailsSubmitted = account.details_submitted;

    db.prepare('UPDATE users SET stripe_onboarded = ?, stripe_details_submitted = ? WHERE stripe_account_id = ?')
      .run(onboarded ? 1 : 0, detailsSubmitted ? 1 : 0, account.id);

    console.log(`✅ Updated account status for ${user.username}: onboarded=${onboarded}, details_submitted=${detailsSubmitted}`);
  } catch (err) {
    console.error('Error handling account.updated:', err);
  }
}

async function handleTransferCreated(transfer) {
  try {
    // Update payout status to 'processing'
    db.prepare('UPDATE payouts SET status = ? WHERE stripe_transfer_id = ?')
      .run('processing', transfer.id);
    
    console.log(`✅ Transfer created: ${transfer.id}`);
  } catch (err) {
    console.error('Error handling transfer.created:', err);
  }
}

async function handleTransferPaid(transfer) {
  try {
    // Update payout status to 'completed'
    db.prepare('UPDATE payouts SET status = ? WHERE stripe_transfer_id = ?')
      .run('completed', transfer.id);
    
    console.log(`✅ Transfer paid: ${transfer.id}`);
  } catch (err) {
    console.error('Error handling transfer.paid:', err);
  }
}

async function handleTransferFailed(transfer) {
  try {
    // Update payout status to 'failed' with error message
    const errorMessage = transfer.failure_message || 'Transfer failed';
    db.prepare('UPDATE payouts SET status = ?, error_message = ? WHERE stripe_transfer_id = ?')
      .run('failed', errorMessage, transfer.id);
    
    console.log(`❌ Transfer failed: ${transfer.id}, reason: ${errorMessage}`);
  } catch (err) {
    console.error('Error handling transfer.failed:', err);
  }
}

module.exports = router;