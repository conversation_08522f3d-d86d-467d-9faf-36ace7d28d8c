# 🚀 Simple Webhook Setup (Just Payments)

## 🎯 **For Right Now - Just Set Up Payment Webhook**

Since Connect events aren't available, let's just set up the payment webhook. This is enough to test everything!

### **What You Need to Do:**

1. **Install ngrok:**
   ```bash
   npm install -g ngrok
   ```

2. **Start ngrok:**
   ```bash
   ngrok http 4242
   ```
   
3. **Copy the HTTPS URL** (e.g., `https://abc123.ngrok.io`)

4. **In Stripe webhook form:**
   - **Endpoint URL:** `https://abc123.ngrok.io/api/payments/webhook`
   - **Description:** `Local testing`
   - **Events:** `checkout.session.completed`
   - **Click "Create destination"**

5. **Copy webhook secret** and update `backend/.env`:
   ```bash
   STRIPE_WEBHOOK_SECRET=whsec_your_actual_secret_here
   ```

6. **Restart backend:**
   ```bash
   npm start
   ```

## ✅ **What Works with Just Payment Webhook:**

- ✅ **User registration** (creates Stripe Connect accounts)
- ✅ **Adding funds** (payment confirmation via webhook)
- ✅ **Instant donations** (balance-based, no webhooks needed)
- ✅ **Balance updates** (real-time)
- ✅ **Cash out initiation** (creates Stripe transfers)

## ⚠️ **What You'll Miss Without Connect Webhooks:**

- ❌ **Cash out status updates** (will stay "processing")
- ❌ **Automatic verification status updates**

**But that's okay for testing!** You can manually check status in Stripe Dashboard.

## 🧪 **Test Everything:**

1. **Register user** → Should work
2. **Add $10** → Should work with webhook confirmation
3. **Donate $0.25** → Should be instant
4. **Try cash out** → Will initiate transfer (check Stripe Dashboard for status)

## 🎯 **Ready to Test!**

Just set up that one payment webhook and you're good to go! 🚀

**Connect webhooks can be added later when you enable Stripe Connect properly in your account.**