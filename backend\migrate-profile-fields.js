const Database = require('better-sqlite3');
const path = require('path');

// Initialize database
const db = new Database(path.join(__dirname, 'hashtagdollar.db'));

console.log('🔄 Starting profile fields migration...');

try {
  // Add new profile fields to users table
  const alterQueries = [
    'ALTER TABLE users ADD COLUMN bio TEXT',
    'ALTER TABLE users ADD COLUMN phone TEXT',
    'ALTER TABLE users ADD COLUMN url1 TEXT',
    'ALTER TABLE users ADD COLUMN url2 TEXT',
    'ALTER TABLE users ADD COLUMN url3 TEXT',
    'ALTER TABLE users ADD COLUMN url4 TEXT',
    'ALTER TABLE users ADD COLUMN url5 TEXT'
  ];

  for (const query of alterQueries) {
    try {
      db.prepare(query).run();
      console.log(`✅ Executed: ${query}`);
    } catch (error) {
      if (error.message.includes('duplicate column name')) {
        console.log(`⚠️  Column already exists: ${query}`);
      } else {
        console.error(`❌ Error executing: ${query}`, error.message);
      }
    }
  }

  // Verify the new columns exist
  const tableInfo = db.prepare("PRAGMA table_info(users)").all();
  const columnNames = tableInfo.map(col => col.name);
  
  console.log('\n📋 Current users table columns:');
  columnNames.forEach(col => console.log(`  - ${col}`));

  const newFields = ['bio', 'phone', 'url1', 'url2', 'url3', 'url4', 'url5'];
  const missingFields = newFields.filter(field => !columnNames.includes(field));
  
  if (missingFields.length === 0) {
    console.log('\n✅ All new profile fields have been successfully added!');
  } else {
    console.log('\n❌ Missing fields:', missingFields);
  }

} catch (error) {
  console.error('❌ Migration failed:', error);
} finally {
  db.close();
  console.log('🔒 Database connection closed.');
}
