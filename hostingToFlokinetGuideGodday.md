Deploy to FlokiNet VPS

SSH into VPS:

ssh unhouseddocs@***************
Username: unhouseddocs
Password: Carlos17!@

Install Node, Nginx:

sudo apt update
sudo apt install nodejs npm nginx -y


Build Flutter web:

cd flutter_app
flutter build web


Copy to VPS:

# 1. Clean old files
ssh unhouseddocs@*************** "rm -rf /var/www/hashtag-dollar/*"
# 2. Upload new build
scp -r build/web/* unhouseddocs@***************:/var/www/hashtag-dollar/


Configure Nginx (/etc/nginx/sites-available/hashtag-dollar):

server {
    listen 80;
    server_name hashtagdollars.com www.hashtagdollars.com;

    root /var/www/hashtag-dollar;
    index index.html;

    location / {
        try_files $uri /index.html;
    }

    location /api/ {
        proxy_pass http://localhost:4242/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}


Enable site and reload Nginx:

sudo ln -s /etc/nginx/sites-available/hashtag-dollar /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx


CPY BACKEND CODES FROM LOCAL MACHINE TO VPS USING ./deploy_backend.sh

Start backend:

cd ~/hashtag-dollar/backend
npm install
node server.js


Verify:

Visit http://hashtagdollars.com → Flutter web loads

API calls → proxied via /api/


Domain config guide https://billing.flokinet.is/index.php?rp=/knowledgebase/57/Nameserver-and-DNS-records.html




