require('dotenv').config();
const express = require('express');
const router = express.Router();
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const Stripe = require('stripe');
const { v4: uuidv4 } = require('uuid');
const multer = require('multer');
const fs = require('fs');
const path = require('path');

const stripe = Stripe(process.env.STRIPE_SECRET_KEY);
const JWT_SECRET = process.env.JWT_SECRET || 'secret123';


// ---------------- Multer Configuration ----------------
// Store uploaded files in memory for base64 conversion
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 10 * 1024 * 1024 }, // limit 10MB per file
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.mimetype)) {
      return cb(new Error('Invalid file type. Only JPEG, PNG, GIF, WebP allowed.'));
    }
    cb(null, true);
  }
});


const { db, findUserByUsername, getUserIdByUsername, recordDonation } = require('../db');

// ---------------- Helper functions ----------------
function findUserByEmail(email) {
  return db.prepare('SELECT * FROM users WHERE email = ?').get(email);
}

function getAllUsers() {
  return db.prepare('SELECT * FROM users').all();
}

// ---------------- Create user ----------------
function createUser({ email, password, username, display_name, full_name }) {
  const hashedPassword = bcrypt.hashSync(password, 10);
  const id = uuidv4();
  const created_at = Date.now();

  db.prepare(`
    INSERT INTO users (
      id, email, password, username, display_name, full_name,
      balance, created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `).run(
    id, email, hashedPassword, username, display_name, full_name,
    0, created_at
  );

  return { id, email, username, display_name, full_name, balance: 0, created_at };
}

// ---------------- Create user with Stripe Connect ----------------
function createUserWithStripe({ email, password, username, display_name, full_name, stripe_account_id }) {
  const hashedPassword = bcrypt.hashSync(password, 10);
  const id = uuidv4();
  const created_at = Date.now();

  db.prepare(`
    INSERT INTO users (
      id, email, password, username, display_name, full_name,
      balance, stripe_account_id, stripe_onboarded, stripe_details_submitted, created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `).run(
    id, email, hashedPassword, username, display_name, full_name,
    0, stripe_account_id, 0, 0, created_at
  );

  return { id, email, username, display_name, full_name, balance: 0, created_at };
}

// ---------------- Register Route ----------------
router.post('/register', async (req, res) => {
  try {
    const { email, password, username, country = 'US' } = req.body;

    if (!email || !password || !username) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    const normalized = username.startsWith('#$') ? username : '#$' + username;

    if (getUserIdByUsername(normalized) || findUserByEmail(email)) {
      return res.status(400).json({ message: 'Email or username already exists' });
    }

    // Create Stripe Connect account for the user
    let stripeAccountId = null;
    try {
      const account = await stripe.accounts.create({
        type: 'express',
        country: country,
        email: email,
        capabilities: {
          transfers: { requested: true },
        },
        business_type: 'individual',
      });
      stripeAccountId = account.id;
      console.log(`✅ Created Stripe Connect account ${account.id} for new user ${normalized}`);
    } catch (stripeErr) {
      console.error('Error creating Stripe account during registration:', stripeErr);
      // Continue with registration even if Stripe account creation fails
      // User can create it later through the onboarding flow
    }

    const user = createUserWithStripe({
      email,
      password,
      username: normalized,
      display_name: null,
      full_name: null,
      stripe_account_id: stripeAccountId
    });

    const token = jwt.sign({ id: user.id }, JWT_SECRET, { expiresIn: '30d' });

    res.json({
      token,
      user: {
        ...user,
        stripe_account_id: stripeAccountId,
        stripe_onboarded: 0,
        stripe_details_submitted: 0
      }
    });

  } catch (err) {
    console.error('Register error:', err);
    res.status(500).json({ message: 'Internal server error' });
  }
});



// PUT update user profile
// ---------------- PUT update user profile ----------------


// ---------------- Upload Profile Picture ----------------
// router.post('/upload-picture', async (req, res) => {
//   try {
//     const { username, profile_picture } = req.body;

//     if (!username || !profile_picture) {
//       return res.status(400).json({ error: 'username and profile_picture required' });
//     }

//     const user = findUserByUsername(username);
//     if (!user) {
//       return res.status(404).json({ error: 'User not found' });
//     }

//     // Here profile_picture can just be stored as string (e.g., filename or base64)
//     db.prepare('UPDATE users SET profile_picture = ? WHERE username = ?')
//       .run(profile_picture, username);

//     return res.json({ success: true, username, profile_picture });

//   } catch (err) {
//     console.error('Upload picture error:', err);
//     return res.status(500).json({ error: err.message });
//   }
// });



// ---------------- Donate Route ----------------
router.post('/donate', async (req, res) => {
  try {
    const { fromUsername, toUsername, amountCents, feeCents, originalAmountCents } = req.body;

    console.log('💡 Donate Route called with:', req.body);

    if (!fromUsername || !toUsername || !amountCents || feeCents == null) {
      return res.status(400).json({
        error: 'fromUsername, toUsername, amountCents, feeCents required'
      });
    }

    // --- Find users ---
    const fromUser = findUserByUsername(fromUsername);
    const toUser = findUserByUsername(toUsername);

    if (!fromUser) {
      return res.status(404).json({ error: 'Donor not found. Please ensure you are logged in.' });
    }

    if (!toUser) {
      return res.status(404).json({ error: 'Recipient not found. Please check the username.' });
    }

    // --- Validate donor has sufficient balance ---
    if (fromUser.balance < amountCents) {
      return res.status(400).json({
        error: 'Insufficient balance',
        currentBalance: fromUser.balance,
        requiredAmount: amountCents
      });
    }

    console.log(`💰 Processing donation: ${fromUsername} (balance: ${fromUser.balance}) -> ${toUsername}`);
    console.log(`💰 Amount: ${amountCents} cents (no fees on donations)`);

    // --- Use database transaction for atomic balance transfer ---
    const donationResult = db.transaction(() => {
      // Deduct amount from donor
      const deductResult = db.prepare('UPDATE users SET balance = balance - ? WHERE username = ? AND balance >= ?')
        .run(amountCents, fromUsername, amountCents);

      if (deductResult.changes === 0) {
        throw new Error('Insufficient balance or user not found');
      }

      // Credit full amount to recipient (no fees on donations)
      db.prepare('UPDATE users SET balance = balance + ? WHERE username = ?')
        .run(amountCents, toUsername);

      // Record donation in history
      const donationId = uuidv4();
      db.prepare(`
        INSERT INTO donation_history (id, from_username, to_username, amount_cents, created_at)
        VALUES (?, ?, ?, ?, ?)
      `).run(donationId, fromUsername, toUsername, amountCents, Date.now());

      // Record transaction for audit trail
      const transactionId = uuidv4();
      db.prepare(`
        INSERT INTO transactions (id, from_user, to_user, amount, type, note, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        transactionId,
        fromUser.id,
        toUser.id,
        amountCents,
        'donation',
        `Donation from ${fromUsername} to ${toUsername}`,
        Date.now()
      );

      return { donationId, transactionId, netAmount: amountCents };
    })();

    // Get updated balances
    const updatedFromUser = findUserByUsername(fromUsername);
    const updatedToUser = findUserByUsername(toUsername);

    console.log(`✅ Donation succeeded: ${fromUsername} -> ${toUsername}`);
    console.log(`✅ Updated balances - ${fromUsername}: ${updatedFromUser.balance}, ${toUsername}: ${updatedToUser.balance}`);

    return res.json({
      success: true,
      fromUsername,
      toUsername,
      amountCents,
      feeCents: 0, // No fees on donations
      netAmount: donationResult.netAmount,
      newBalances: {
        donor: updatedFromUser.balance,
        recipient: updatedToUser.balance
      },
      donationId: donationResult.donationId,
      transactionId: donationResult.transactionId
    });

  } catch (err) {
    console.error('Donation error:', err);
    return res.status(500).json({ error: err.message });
  }
});


// ---------------- Donation History Route ----------------
router.post('/my-donations', (req, res) => {
  try {
    const { username } = req.body;
    if (!username) return res.status(400).json({ error: 'username is required' });

    const user = findUserByUsername(username);
    if (!user) return res.status(404).json({ message: 'User not found' });

    const donated = db.prepare(`
      SELECT id, to_username AS recipient, amount_cents, created_at
      FROM donation_history
      WHERE from_username = ?
      ORDER BY created_at DESC
    `).all(username);

    const received = db.prepare(`
      SELECT id, from_username AS donor, amount_cents, created_at
      FROM donation_history
      WHERE to_username = ?
      ORDER BY created_at DESC
    `).all(username);

    return res.json({ username, donated, received });

  } catch (err) {
    console.error('Donation history error:', err);
    return res.status(500).json({ error: err.message });
  }
});


// ---------------- Get User Balance ----------------
// ---------------- Get User Balance ----------------
router.post('/balance', (req, res) => {
  try {
    console.log("DEBUG /balance body:", req.body);  // ✅ log incoming body

    const { username } = req.body;
    if (!username) {
      console.warn("⚠️ Missing username in request");
      return res.status(400).json({ error: 'username is required' });
    }

    const user = findUserByUsername(username);
    if (!user) {
      console.warn(`⚠️ User not found: ${username}`);
      return res.status(404).json({ error: 'User not found' });
    }

    console.log(`✅ Found user: ${user.username}, balance=${user.balance}`);

    return res.json({
      username: user.username,
      balance: user.balance
    });

  } catch (err) {
    console.error('Get balance error:', err);
    return res.status(500).json({ error: err.message });
  }
});




// ---------------- Cash Out Route ----------------
router.post('/cashout', async (req, res) => {
  try {
    const { username, amountCents } = req.body;

    if (!username || !amountCents) {
      return res.status(400).json({ error: 'username and amountCents required' });
    }

    const user = findUserByUsername(username);
    if (!user) return res.status(404).json({ error: 'User not found' });
    if (amountCents < 500) return res.status(400).json({ error: 'Minimum $5 required to cash out' });
    if (user.balance < amountCents) return res.status(400).json({ error: 'Insufficient balance' });

    // Check if user has Stripe Connect account
    if (!user.stripe_account_id) {
      return res.status(400).json({
        error: 'Stripe account required for cashout',
        needsOnboarding: true
      });
    }

    // Check if user has completed Stripe onboarding
    if (!user.stripe_onboarded) {
      // Double-check with Stripe API
      try {
        const account = await stripe.accounts.retrieve(user.stripe_account_id);
        const onboarded = account.details_submitted && account.charges_enabled && account.payouts_enabled;

        if (!onboarded) {
          return res.status(400).json({
            error: 'Please complete Stripe verification to enable cashouts',
            needsOnboarding: true,
            accountId: user.stripe_account_id
          });
        }

        // Update local database
        db.prepare('UPDATE users SET stripe_onboarded = 1 WHERE username = ?').run(username);
      } catch (stripeErr) {
        console.error('Error checking Stripe account:', stripeErr);
        return res.status(500).json({ error: 'Error verifying Stripe account' });
      }
    }

    const payoutId = uuidv4();

    // Apply 10% platform fee on cashout (user gets 90%)
    const platformFee = Math.floor(amountCents * 0.10);
    const userReceives = amountCents - platformFee;
    
    console.log(`💰 Cashout: ${amountCents} cents requested, ${platformFee} cents fee, ${userReceives} cents to user`);

    try {
      let transfer;

      // In test mode, simulate transfer if balance insufficient error
      if (process.env.NODE_ENV === 'development') {
        try {
          // Try real transfer first
          transfer = await stripe.transfers.create({
            amount: userReceives,
            currency: 'usd',
            destination: user.stripe_account_id,
            description: `Cashout for ${username} (90% after 10% platform fee)`,
            metadata: {
              username: username,
              payoutId: payoutId,
              originalAmount: amountCents,
              platformFee: platformFee
            }
          });
        } catch (stripeErr) {
          if (stripeErr.code === 'balance_insufficient') {
            // Simulate successful transfer in test mode
            console.log('🧪 Simulating transfer due to insufficient test balance');
            transfer = {
              id: `tr_test_${payoutId}`,
              amount: userReceives,
              currency: 'usd',
              destination: user.stripe_account_id,
              description: `Test cashout for ${username} (90% after 10% platform fee)`,
              created: Math.floor(Date.now() / 1000)
            };
          } else {
            throw stripeErr; // Re-throw other errors
          }
        }
      } else {
        // Production mode - always try real transfer
        transfer = await stripe.transfers.create({
          amount: userReceives,
          currency: 'usd',
          destination: user.stripe_account_id,
          description: `Cashout for ${username} (90% after 10% platform fee)`,
          metadata: {
            username: username,
            payoutId: payoutId,
            originalAmount: amountCents,
            platformFee: platformFee
          }
        });
      }

      // Use database transaction for atomic operation
      const cashoutResult = db.transaction(() => {
        // Deduct amount from user balance
        const deductResult = db.prepare('UPDATE users SET balance = balance - ? WHERE username = ? AND balance >= ?')
          .run(amountCents, username, amountCents);

        if (deductResult.changes === 0) {
          throw new Error('Insufficient balance or user not found');
        }

        // Record payout in database
        db.prepare(`
          INSERT INTO payouts (
            id, username, amount_cents, stripe_account_id, stripe_transfer_id, 
            status, timestamp
          ) VALUES (?, ?, ?, ?, ?, ?, ?)
        `).run(
          payoutId,
          username,
          amountCents,
          user.stripe_account_id,
          transfer.id,
          'processing',
          Date.now()
        );

        return { transferId: transfer.id };
      })();

      console.log(`✅ Cashout initiated: ${username} cashed out ${amountCents} cents, transfer: ${transfer.id}`);

      return res.json({
        success: true,
        username,
        amountCents,
        message: 'Cashout initiated successfully. Funds will arrive in 1-2 business days.',
        payoutId: payoutId,
        transferId: transfer.id,
        status: 'processing'
      });

    } catch (stripeErr) {
      console.error('Stripe transfer error:', stripeErr);

      // Record failed payout
      db.prepare(`
        INSERT INTO payouts (
          id, username, amount_cents, stripe_account_id, 
          status, error_message, timestamp
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        payoutId,
        username,
        amountCents,
        user.stripe_account_id,
        'failed',
        stripeErr.message,
        Date.now()
      );

      return res.status(400).json({
        error: 'Cashout failed: ' + stripeErr.message,
        payoutId: payoutId
      });
    }

  } catch (err) {
    console.error('Cashout error:', err);
    return res.status(500).json({ error: err.message });
  }
});


// ---------------- List users ----------------
router.get('/', (req, res) => {
  const users = getAllUsers();
  res.json(users);
});

// ---------------- Get profile (private - includes sensitive data) ----------------
router.get('/:username', (req, res) => {
  const username = decodeURIComponent(req.params.username);
  const u = findUserByUsername(username);
  if (!u) return res.status(404).json({ message: 'Not found' });
  res.json({
    user: {
      id: u.id,
      username: u.username,
      email: u.email,
      password: u.password,
      display_name: u.display_name,
      full_name: u.full_name,
      balance: u.balance,
      profile_picture: u.profile_picture,
      country: u.country,
      state: u.state,
      category: u.category,
      bio: u.bio,
      phone: u.phone,
      url1: u.url1,
      url2: u.url2,
      url3: u.url3,
      url4: u.url4,
      url5: u.url5,
      created_at: u.created_at
    }
  });
});

// ---------------- Get public profile (excludes sensitive data) ----------------
router.get('/public/:username', (req, res) => {
  const username = decodeURIComponent(req.params.username);
  const u = findUserByUsername(username);
  if (!u) return res.status(404).json({ message: 'User not found' });

  res.json({
    user: {
      id: u.id,
      username: u.username,
      display_name: u.display_name,
      full_name: u.full_name,
      profile_picture: u.profile_picture,
      country: u.country,
      state: u.state,
      category: u.category,
      bio: u.bio,
      url1: u.url1,
      url2: u.url2,
      url3: u.url3,
      url4: u.url4,
      url5: u.url5,
      created_at: u.created_at
      // Excluded: email, password, balance, phone
    }
  });
});

// ---------------- Update profile ----------------
router.put('/:username', upload.single('profile_picture'), (req, res) => {
  try {
    const username = decodeURIComponent(req.params.username);
    const user = findUserByUsername(username);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    console.log('Profile update request for:', username);
    console.log('Request body:', req.body);
    console.log('Request file:', req.file ? req.file.originalname : 'No file');

    // Handle profile picture upload first
    let profilePicturePath = user.profile_picture;
    if (req.file) {
      console.log('File received:', {
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size,
        buffer: req.file.buffer ? 'Buffer exists' : 'Buffer is undefined'
      });

      // Validate file size (additional check)
      if (req.file.size > 10 * 1024 * 1024) {
        return res.status(400).json({
          message: 'File too large. Maximum size is 10MB.'
        });
      }

      if (req.file.buffer) {
        try {
          // Convert image buffer to base64 and store in database
          const base64Image = req.file.buffer.toString('base64');
          const mimeType = req.file.mimetype;
          profilePicturePath = `data:${mimeType};base64,${base64Image}`;
          console.log('Profile picture saved as base64 in database, size:', base64Image.length);
        } catch (bufferError) {
          console.error('Error converting buffer to base64:', bufferError);
          return res.status(500).json({
            message: 'Error processing image file. Please try a smaller image or different format.'
          });
        }
      } else {
        console.log('File buffer is undefined, skipping image save');
        return res.status(400).json({
          message: 'Invalid image file. Please try again.'
        });
      }
    }

    // Update database - only update fields that exist in the database
    const updateFields = [];
    const updateValues = [];

    if (req.body.password) {
      updateFields.push('password = ?');
      updateValues.push(req.body.password);
    }

    if (req.body.display_name !== undefined) {
      updateFields.push('display_name = ?');
      updateValues.push(req.body.display_name || null);
    }

    if (req.body.full_name !== undefined) {
      updateFields.push('full_name = ?');
      updateValues.push(req.body.full_name || null);
    }

    if (req.body.country !== undefined) {
      updateFields.push('country = ?');
      updateValues.push(req.body.country || null);
    }

    if (req.body.state !== undefined) {
      updateFields.push('state = ?');
      updateValues.push(req.body.state || null);
    }

    if (req.body.category !== undefined) {
      updateFields.push('category = ?');
      updateValues.push(req.body.category || null);
    }

    if (req.body.bio !== undefined) {
      updateFields.push('bio = ?');
      updateValues.push(req.body.bio || null);
    }

    if (req.body.phone !== undefined) {
      updateFields.push('phone = ?');
      updateValues.push(req.body.phone || null);
    }

    if (req.body.url1 !== undefined) {
      updateFields.push('url1 = ?');
      updateValues.push(req.body.url1 || null);
    }

    if (req.body.url2 !== undefined) {
      updateFields.push('url2 = ?');
      updateValues.push(req.body.url2 || null);
    }

    if (req.body.url3 !== undefined) {
      updateFields.push('url3 = ?');
      updateValues.push(req.body.url3 || null);
    }

    if (req.body.url4 !== undefined) {
      updateFields.push('url4 = ?');
      updateValues.push(req.body.url4 || null);
    }

    if (req.body.url5 !== undefined) {
      updateFields.push('url5 = ?');
      updateValues.push(req.body.url5 || null);
    }

    if (profilePicturePath !== user.profile_picture) {
      updateFields.push('profile_picture = ?');
      updateValues.push(profilePicturePath);
    }

    // Update database with transaction for safety
    const dbUpdateSuccess = db.transaction(() => {
      if (updateFields.length > 0) {
        updateValues.push(username); // Add username for WHERE clause

        const sql = `UPDATE users SET ${updateFields.join(', ')} WHERE username = ?`;
        console.log('SQL Query:', sql);
        console.log('Values:', updateValues);

        const result = db.prepare(sql).run(...updateValues);
        if (result.changes === 0) {
          throw new Error('No rows updated - user may not exist');
        }
      }
      return true;
    })();

    if (!dbUpdateSuccess) {
      return res.status(500).json({ message: 'Failed to update profile in database' });
    }

    // Get updated user data
    const updatedUser = findUserByUsername(username);
    if (!updatedUser) {
      return res.status(500).json({ message: 'Failed to retrieve updated user data' });
    }

    console.log('Profile updated successfully for:', username);

    res.json({
      message: 'Profile updated successfully',
      user: {
        id: updatedUser.id,
        username: updatedUser.username,
        email: updatedUser.email,
        display_name: updatedUser.display_name,
        full_name: updatedUser.full_name,
        balance: updatedUser.balance,
        profile_picture: updatedUser.profile_picture,
        country: updatedUser.country,
        state: updatedUser.state,
        category: updatedUser.category,
        bio: updatedUser.bio,
        phone: updatedUser.phone,
        url1: updatedUser.url1,
        url2: updatedUser.url2,
        url3: updatedUser.url3,
        url4: updatedUser.url4,
        url5: updatedUser.url5,
        created_at: updatedUser.created_at
      }
    });

  } catch (err) {
    console.error('Profile update error:', err);

    // Provide more specific error messages
    if (err.message && err.message.includes('file')) {
      res.status(400).json({
        message: 'File upload error',
        error: err.message
      });
    } else if (err.message && err.message.includes('SQLITE_')) {
      res.status(500).json({
        message: 'Database error',
        error: 'Please try again later'
      });
    } else {
      res.status(500).json({
        message: 'Internal server error',
        error: err.message || 'Unknown error occurred'
      });
    }
  }
});

// ---------------- Get All Users Route ----------------


// Add a giver to the list of favorites
router.post('/favorite-giver', (req, res) => {
  try {
    const { username, giverUsername } = req.body;

    if (!username || !giverUsername) {
      return res.status(400).json({ error: 'Both username and giverUsername are required' });
    }

    const user = findUserByUsername(username);
    const giver = findUserByUsername(giverUsername);

    if (!user || !giver) {
      return res.status(404).json({ error: 'User or Giver not found' });
    }

    // Add to the favorite_givers table
    const existingFavorite = db.prepare(`
      SELECT * FROM favorite_givers WHERE user_id = ? AND giver_id = ?
    `).get(user.id, giver.id);

    if (existingFavorite) {
      return res.status(400).json({ message: 'This giver is already in your favorites' });
    }

    db.prepare(`
      INSERT INTO favorite_givers (user_id, giver_id) VALUES (?, ?)
    `).run(user.id, giver.id);

    return res.json({ success: true, message: 'Giver added to favorites' });

  } catch (err) {
    console.error('Error adding giver to favorites:', err);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

// View all favorite givers for a user
router.post('/favorite-givers', (req, res) => {
  try {
    const { username } = req.body;

    if (!username) {
      return res.status(400).json({ error: 'Username is required' });
    }

    const user = findUserByUsername(username);

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const favoriteGivers = db.prepare(`
      SELECT u.username, u.profile_picture
      FROM users u
      JOIN favorite_givers fg ON fg.giver_id = u.id
      WHERE fg.user_id = ?
    `).all(user.id);

    return res.json({ favoriteGivers });

  } catch (err) {
    console.error('Error fetching favorite givers:', err);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

// Get total amount received from givers in the last 30 days
router.post('/total-received', (req, res) => {
  try {
    const { username } = req.body;

    if (!username) {
      return res.status(400).json({ error: 'Username is required' });
    }

    const user = findUserByUsername(username);

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const donations = db.prepare(`
      SELECT SUM(amount_cents) as total_received
      FROM donation_history
      WHERE to_username = ? AND created_at >= ?
    `).get(username, Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago

    return res.json({ totalReceived: donations.total_received || 0 });

  } catch (err) {
    console.error('Error calculating total received:', err);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

// ---------------- Get Payout History ----------------
router.post('/payout-history', (req, res) => {
  try {
    const { username } = req.body;

    if (!username) {
      return res.status(400).json({ error: 'Username is required' });
    }

    const user = findUserByUsername(username);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const payouts = db.prepare(`
      SELECT id, amount_cents, status, stripe_transfer_id, error_message, timestamp
      FROM payouts
      WHERE username = ?
      ORDER BY timestamp DESC
    `).all(username);

    return res.json({
      username,
      payouts: payouts.map(payout => ({
        id: payout.id,
        amountCents: payout.amount_cents,
        status: payout.status,
        transferId: payout.stripe_transfer_id,
        errorMessage: payout.error_message,
        timestamp: payout.timestamp,
        createdAt: new Date(payout.timestamp).toISOString()
      }))
    });

  } catch (err) {
    console.error('Error fetching payout history:', err);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
