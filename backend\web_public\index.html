<!DOCTYPE html>
<html>
<head>
    <title>Public Donation</title>
</head>
<body>
    <h1>Make a Donation</h1>
    <form id="donationForm">
        <label>Name:</label>
        <input type="text" name="name" required /><br>

        <label>Email:</label>
        <input type="email" name="email" required /><br>

        <label>Amount (USD):</label>
        <input type="number" name="amount" required /><br>

        <button type="submit">Donate</button>
    </form>

    <script>
        document.getElementById("donationForm").addEventListener("submit", async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const body = {
                name: formData.get("name"),
                email: formData.get("email"),
                amount: parseFloat(formData.get("amount"))
            };

            try {
                const res = await fetch("http://localhost:5000/api/payments/donate", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify(body)
                });

                const data = await res.json();
                alert(data.message || "Donation successful!");
            } catch (error) {
                alert("Error: " + error.message);
            }
        });
    </script>
</body>
</html>
