import 'package:flutter/material.dart';
import '../widgets/navigation_bar.dart';
import '../widgets/footer.dart';

class TermsScreen extends StatelessWidget {
  const TermsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // extendBodyBehindAppBar: true,
      // appBar: const PreferredSize(
      //   preferredSize: Size.fromHeight(60),
      //   child: AppNavigationBar(),
      // ),
      body: Column(
        children: [
          Expanded(
            child: Container(
              width: double.infinity,
              color: const Color(0xFFB76E79), // Rose Gold background
              child: Center(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Container(
                    padding: const EdgeInsets.all(24.0),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Back Button
                        Align(
                          alignment: Alignment.centerLeft,
                          child: IconButton(
                            onPressed: () => Navigator.of(context).pop(),
                            icon: const Icon(Icons.arrow_back,
                                color: Colors.white),
                          ),
                        ),
                        const SizedBox(height: 10),

                        // Logo
                        Image.asset('assets/logo.png', height: 100),
                        const SizedBox(height: 20),

                        // Title
                        const Text(
                          'Terms & Conditions',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 20),

                        // Terms content
                        const Text(
                          'Welcome to Hashtag Dollars. By accessing or using our platform, you agree to comply with the following Terms & Conditions. '
                          'Please read them carefully.\n\n'
                          '1. Account Responsibility: Users are responsible for maintaining the confidentiality of their account information, '
                          'including passwords and login credentials. Any activity under your account is your responsibility.\n\n'
                          '2. Platform Usage: Hashtag Dollars is designed to facilitate financial transactions and social connections. Users '
                          'agree not to misuse the platform, post fraudulent information, or engage in illegal activities.\n\n'
                          '3. Transactions: All transactions conducted through Hashtag Dollars are subject to verification and compliance with local '
                          'laws and regulations. We are not liable for errors caused by incorrect input or third-party processing delays.\n\n'
                          '4. Content Ownership: Users retain ownership of content they upload but grant Hashtag Dollars the right to display and '
                          'share such content within the platform. Users agree not to upload offensive, harmful, or illegal content.\n\n'
                          '5. Limitation of Liability: Hashtag Dollars is provided "as is." We do not guarantee uninterrupted service, and '
                          'we are not liable for any direct or indirect damages resulting from platform usage, including financial loss.\n\n'
                          '6. Modifications: We reserve the right to modify or terminate services, fees, and these Terms & Conditions at any time. '
                          'Changes will be communicated via email or notifications on the platform.\n\n'
                          '7. Termination: Accounts violating our Terms & Conditions may be suspended or terminated without prior notice. Users '
                          'agree to comply with all rules set forth by Hashtag Dollars.\n\n'
                          '8. Governing Law: These Terms & Conditions are governed by the laws of United States. Any disputes arising from platform usage '
                          'will be subject to the jurisdiction of the United States courts.\n\n'
                          'By using Hashtag Dollars, you acknowledge that you have read, understood, and agreed to these Terms & Conditions. '
                          'Your continued use constitutes acceptance of any updates or changes.',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                            height: 1.6,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: const AppFooter(),
    );
  }
}
