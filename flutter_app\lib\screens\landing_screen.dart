import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import '../widgets/navigation_bar.dart';
import '../widgets/footer.dart';
import '../constants/theme_constants.dart';

class LandingScreen extends StatefulWidget {
  const LandingScreen({super.key});

  @override
  State<LandingScreen> createState() => _LandingScreenState();
}

class _LandingScreenState extends State<LandingScreen> {
  late VideoPlayerController _videoCtrl;
  bool _isVideoInitialized = false;
  bool _isPlaying = false;

  @override
  void initState() {
    super.initState();
    _videoCtrl = VideoPlayerController.networkUrl(
      Uri.parse(
          'https://zeely.ws/e5e343a4-a1ab-4214-a471-c18de45e424f/5b8f7c99-b45c-43e5-ac4e-563b91637c09/video_hook.mp4'),
    )..initialize().then((_) {
        if (mounted) {
          setState(() {
            _isVideoInitialized = true;
          });
        }
      }).catchError((error) {
        debugPrint('Video initialization error: $error');
      });
  }

  @override
  void dispose() {
    _videoCtrl.dispose();
    super.dispose();
  }

  void _toggleVideo() {
    if (_videoCtrl.value.isPlaying) {
      _videoCtrl.pause();
      setState(() => _isPlaying = false);
    } else {
      _videoCtrl.play();
      setState(() => _isPlaying = true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: const PreferredSize(
        preferredSize: Size.fromHeight(60),
        child: AppNavigationBar(),
      ),
      body: Column(
        children: [
          Expanded(
            child: Container(
              color: AppColors.roseGold, // Rose Gold background
              width: double.infinity,
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 700),
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const SizedBox(height: 24),
                        Image.asset(
                          'assets/logo.png',
                          height: 100,
                        ),
                        const SizedBox(height: 24),
                        const Text(
                          "Hashtag Dollars [#\$]",
                          textAlign: TextAlign.center,
                          style: AppTextStyles.largeTitle,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          "HashTag Dollars [#\$] is available to everyone, "
                          "but it was created by a Black African American Tech Entrepreneur "
                          "for the Descendants of Black Africans in America, Africa and those "
                          "in the Diaspora, including individuals, families, artists, HBCUs, "
                          "athletes, entertainers, students, nonprofits, small businesses, "
                          "startups, corporations, churches, and mayors for economic development.",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 32),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ElevatedButton(
                              onPressed: () =>
                                  Navigator.pushNamed(context, '/login'),
                              style: AppButtonStyles.primaryButton,
                              child: const Text('Login'),
                            ),
                            const SizedBox(width: 16),
                            ElevatedButton(
                              onPressed: () =>
                                  Navigator.pushNamed(context, '/register'),
                              style: AppButtonStyles.primaryButton,
                              child: const Text('Register'),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        _isVideoInitialized
                            ? Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    height: 200,
                                    child: VideoPlayer(_videoCtrl),
                                  ),
                                  const SizedBox(height: 12),
                                  ElevatedButton(
                                    onPressed: _toggleVideo,
                                    style: AppButtonStyles.secondaryButton,
                                    child: Text(_isPlaying
                                        ? "Pause Video"
                                        : "Play Video"),
                                  ),
                                ],
                              )
                            : const CircularProgressIndicator(
                                color: Colors.white),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
          const AppFooter(),
        ],
      ),
    );
  }
}
