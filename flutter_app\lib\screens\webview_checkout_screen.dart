import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebviewScreen extends StatefulWidget {
  final String initialUrl;
  const WebviewScreen({super.key, required this.initialUrl});

  @override
  State<WebviewScreen> createState() => _WebviewScreenState();
}

class _WebviewScreenState extends State<WebviewScreen> {
  late final WebViewController _controller;

  @override
  void initState() {
    super.initState();

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (url) {
            print('Page finished loading: $url');
          },
          onNavigationRequest: (request) {
            // ✅ Detect success or cancel
            if (request.url.contains('/success')) {
              Navigator.pushReplacementNamed(context, '/success');
              return NavigationDecision.prevent;
            } else if (request.url.contains('/cancel')) {
              Navigator.pushReplacementNamed(context, '/cancel');
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.initialUrl));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Stripe Checkout')),
      body: WebViewWidget(controller: _controller),
    );
  }
}
