import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../services/api.dart';
import 'package:image_picker/image_picker.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import '../constants/theme_constants.dart';
import '../widgets/enhanced_share_widget.dart';
import '../widgets/country_state_city_picker.dart';

class UserProfileScreen extends StatefulWidget {
  final String username; // Logged-in user
  const UserProfileScreen({super.key, required this.username});

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  final _formKey = GlobalKey<FormState>();

  // Controllers
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _usernameController = TextEditingController();
  final _displayNameController = TextEditingController();
  final _fullNameController = TextEditingController();
  final _balanceController = TextEditingController();
  final _countryController = TextEditingController();
  final _stateController = TextEditingController();
  final _bioController = TextEditingController();
  final _phoneController = TextEditingController();
  final _url1Controller = TextEditingController();
  final _url2Controller = TextEditingController();
  final _url3Controller = TextEditingController();
  final _url4Controller = TextEditingController();
  final _url5Controller = TextEditingController();
  final _cityController = TextEditingController();
  String? _selectedCategory;
  String? countryValue;
  String? stateValue;
  String? cityValue;

  File? _imageFile;
  Uint8List? _webImage;
  String? _profilePictureUrl;

  final String baseUrl = Api.baseUrl + '/api/users';

  final List<String> _categories = [
    'Individual',
    'Black Individual',
    'Family',
    'Black Family',
    'Artist',
    'Black Artist',
    'College',
    'HBCU',
    'Athlete',
    'Black Athlete',
    'Entertainer',
    'Black Entertainer',
    'Student',
    'Black Student',
    'Nonprofit',
    'Black Nonprofit',
    'Small Business',
    'Black Small Business',
    'Corporation',
    'Black Corporation',
    'Religion and Government',
    'Black Government'
  ];

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  Future<void> _loadUserProfile() async {
    try {
      final encUsername = Uri.encodeComponent(widget.username);
      final res = await http.get(Uri.parse('$baseUrl/$encUsername'));

      if (res.statusCode == 200) {
        final data = json.decode(res.body) as Map<String, dynamic>;
        final userData = data['user'] ?? data;

        _setControllers(userData);

        _profilePictureUrl = userData['profile_picture']?.toString();

        // Handle profile picture URL
        if (_profilePictureUrl != null && _profilePictureUrl!.isNotEmpty) {
          // If it's a base64 data URL, extract the bytes
          if (_profilePictureUrl!.startsWith('data:image')) {
            try {
              // Extract base64 data from data URL
              final base64Data = _profilePictureUrl!.split(',')[1];
              final bytes = base64Decode(base64Data);
              setState(() => _webImage = bytes);
            } catch (e) {
              debugPrint('Error decoding base64 image: $e');
            }
          } else if (!_profilePictureUrl!.startsWith('http')) {
            // If it's a relative path, construct full URL
            _profilePictureUrl = '${Api.base}$_profilePictureUrl';

            // Preload image bytes for Web
            if (kIsWeb) {
              try {
                final pictureRes =
                    await http.get(Uri.parse(_profilePictureUrl!));
                if (pictureRes.statusCode == 200) {
                  setState(() => _webImage = pictureRes.bodyBytes);
                }
              } catch (e) {
                debugPrint('Error loading profile picture: $e');
              }
            }
          }
        }
      } else {
        Fluttertoast.showToast(msg: "Failed to load profile");
      }
    } catch (e) {
      Fluttertoast.showToast(msg: "Error loading profile");
      debugPrint("Error loading profile: $e");
    }
  }

  void _setControllers(Map<String, dynamic> data) {
    setState(() {
      _emailController.text = data['email']?.toString() ?? '';
      _passwordController.text = data['password']?.toString() ?? '';
      _usernameController.text = data['username']?.toString() ?? '';
      _displayNameController.text = data['display_name']?.toString() ?? '';
      _fullNameController.text = data['full_name']?.toString() ?? '';
      _balanceController.text = data['balance'] != null
          ? '\$${(double.tryParse(data['balance'].toString()) ?? 0) / 100}'
          : '';
      _countryController.text = data['country']?.toString() ?? '';
      _stateController.text = data['state']?.toString() ?? '';
      _cityController.text = data['city']?.toString() ?? '';
      countryValue = data['country']?.toString();
      stateValue = data['state']?.toString();
      cityValue = data['city']?.toString();
      _bioController.text = data['bio']?.toString() ?? '';
      _phoneController.text = data['phone']?.toString() ?? '';
      _url1Controller.text = data['url1']?.toString() ?? '';
      _url2Controller.text = data['url2']?.toString() ?? '';
      _url3Controller.text = data['url3']?.toString() ?? '';
      _url4Controller.text = data['url4']?.toString() ?? '';
      _url5Controller.text = data['url5']?.toString() ?? '';

      final categoryValue = data['category']?.toString();
      _selectedCategory =
          _categories.contains(categoryValue) ? categoryValue : null;
    });
  }

  Future<void> _pickImage() async {
    final pickedFile = await ImagePicker().pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024);
    if (pickedFile != null) {
      if (kIsWeb) {
        final bytes = await pickedFile.readAsBytes();
        // Additional compression for web
        if (bytes.length > 2 * 1024 * 1024) {
          // If still > 2MB
          // For very large images, we'll let the backend handle it
          // but show a warning
          Fluttertoast.showToast(
              msg: 'Large image selected. Upload may take longer.');
        }
        setState(() => _webImage = bytes);
      } else {
        setState(() => _imageFile = File(pickedFile.path));
      }
    }
  }

  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      final encUsername = Uri.encodeComponent(widget.username);
      final uri = Uri.parse('$baseUrl/$encUsername');

      var request = http.MultipartRequest('PUT', uri);

      // Add text fields (excluding email and username as they shouldn't be editable)
      if (_passwordController.text.isNotEmpty) {
        request.fields['password'] = _passwordController.text;
      }
      request.fields['display_name'] = _displayNameController.text;
      request.fields['full_name'] = _fullNameController.text;
      request.fields['country'] = countryValue ?? '';
      request.fields['state'] = stateValue ?? '';
      request.fields['city'] = cityValue ?? '';
      request.fields['bio'] = _bioController.text;
      request.fields['phone'] = _phoneController.text;
      request.fields['url1'] = _url1Controller.text;
      request.fields['url2'] = _url2Controller.text;
      request.fields['url3'] = _url3Controller.text;
      request.fields['url4'] = _url4Controller.text;
      request.fields['url5'] = _url5Controller.text;
      request.fields['category'] = _selectedCategory ?? '';

      // Add profile picture
      if (_imageFile != null && !kIsWeb) {
        request.files.add(await http.MultipartFile.fromPath(
          'profile_picture',
          _imageFile!.path,
          contentType: MediaType('image', 'jpeg'),
        ));
      } else if (_webImage != null && kIsWeb) {
        request.files.add(http.MultipartFile.fromBytes(
          'profile_picture',
          _webImage!,
          filename: 'profile_picture.jpg',
          contentType: MediaType('image', 'jpeg'),
        ));
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        final resData = json.decode(response.body) as Map<String, dynamic>;
        final updatedUser = resData['user'];

        _setControllers(updatedUser);

        // Handle updated profile picture URL
        final newProfilePicture = updatedUser['profile_picture']?.toString();
        if (newProfilePicture != null && newProfilePicture.isNotEmpty) {
          if (newProfilePicture.startsWith('data:image')) {
            // It's a base64 data URL, handle it directly
            _profilePictureUrl = newProfilePicture;
            // Extract bytes for display
            try {
              final base64Data = newProfilePicture.split(',')[1];
              final bytes = base64Decode(base64Data);
              setState(() {
                _webImage = bytes;
                _imageFile = null; // Clear any temporary file data
              });
            } catch (e) {
              debugPrint('Error decoding updated base64 image: $e');
            }
          } else if (!newProfilePicture.startsWith('http')) {
            _profilePictureUrl = '${Api.base}$newProfilePicture';
          } else {
            _profilePictureUrl = newProfilePicture;
          }
        } else {
          // No profile picture, clear the display
          setState(() {
            _profilePictureUrl = null;
            _webImage = null;
            _imageFile = null;
          });
        }

        Fluttertoast.showToast(msg: "Profile updated successfully");
        // Go back
        Navigator.pop(context);
      } else {
        Fluttertoast.showToast(msg: "Update failed");
      }
    } catch (e) {
      Fluttertoast.showToast(msg: "Error updating profile");
      debugPrint("Error updating profile: $e");
    }
  }

  void _showShareBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.cardBackground,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => EnhancedShareWidget(
        contentId: widget.username,
        contentText: _bioController.text.isNotEmpty
            ? _bioController.text
            : _displayNameController.text,
        contentType: 'profile',
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("User Profile"),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            tooltip: 'Share Profile',
            onPressed: _showShareBottomSheet,
          ),
        ],
      ),
      body: Container(
        color: AppColors.roseGold, // Rose Gold background
        child: Center(
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 600),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          // Logo
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Image.asset('assets/logo.png', height: 50),
                              // Title
                              const Text(
                                'Hashtag Dollars',
                                style: AppTextStyles.title,
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          GestureDetector(
                            onTap: _pickImage,
                            child: CircleAvatar(
                              radius: 60,
                              backgroundColor: Colors.grey[200],
                              backgroundImage: (_webImage != null)
                                  ? MemoryImage(_webImage!) as ImageProvider
                                  : (_imageFile != null)
                                      ? FileImage(_imageFile!) as ImageProvider
                                      : (_profilePictureUrl != null &&
                                              _profilePictureUrl!.isNotEmpty &&
                                              !_profilePictureUrl!
                                                  .startsWith('data:image'))
                                          ? NetworkImage(_profilePictureUrl!)
                                              as ImageProvider
                                          : null,
                              child: (_imageFile == null &&
                                      _webImage == null &&
                                      (_profilePictureUrl == null ||
                                          _profilePictureUrl!.isEmpty ||
                                          _profilePictureUrl!
                                              .startsWith('data:image')))
                                  ? const Icon(Icons.camera_alt,
                                      size: 40, color: Colors.grey)
                                  : null,
                            ),
                          ),
                          const SizedBox(height: 20),
                          // _buildTextField("Password", _passwordController,
                          //     obscure: true),
                          _buildReadOnlyField("Username", _usernameController),
                          // _buildTextField(
                          //     "Display Name", _displayNameController),
                          _buildTextField("Full Name", _fullNameController),
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: DropdownButtonFormField<String>(
                              dropdownColor: AppColors.roseGold,
                              initialValue:
                                  _categories.contains(_selectedCategory)
                                      ? _selectedCategory
                                      : null,
                              decoration: AppInputDecorations.primaryInput(
                                hintText: 'Category',
                              ),
                              style: AppTextStyles.inputText,
                              items: _categories
                                  .map((cat) => DropdownMenuItem(
                                        value: cat,
                                        child: Text(cat,
                                            style: AppTextStyles.inputText),
                                      ))
                                  .toList(),
                              onChanged: (val) =>
                                  setState(() => _selectedCategory = val),
                            ),
                          ),
                          _buildTextField("Bio", _bioController),
                          _buildReadOnlyField("Email", _emailController),
                          _buildTextField("Phone", _phoneController),
                          _buildReadOnlyField("Balance", _balanceController),
                          CountryStateCityPicker(
                            initialCountry: countryValue,
                            initialState: stateValue,
                            initialCity: cityValue,
                            onChanged: (country, state, city) {
                              setState(() {
                                countryValue = country;
                                stateValue = state;
                                cityValue = city;
                              });
                            },
                          ),

                          _buildUrlField("URL 1", _url1Controller),
                          _buildUrlField("URL 2", _url2Controller),
                          _buildUrlField("URL 3", _url3Controller),
                          _buildUrlField("URL 4", _url4Controller),
                          _buildUrlField("URL 5", _url5Controller),

                          const SizedBox(height: 20),
                          ElevatedButton(
                            style: AppButtonStyles.primaryButton,
                            onPressed: _updateProfile,
                            child: const Text("Update Profile"),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField(String label, TextEditingController controller,
      {bool obscure = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: TextFormField(
        controller: controller,
        obscureText: obscure,
        style: AppTextStyles.inputText,
        decoration: AppInputDecorations.primaryInput(
          hintText: label,
        ),
      ),
    );
  }

  Widget _buildReadOnlyField(String label, TextEditingController controller) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: TextFormField(
        controller: controller,
        readOnly: true,
        enabled: false,
        style: AppTextStyles.inputText,
        decoration: AppInputDecorations.primaryInput(
          hintText: label,
        ).copyWith(
          filled: true,
        ),
      ),
    );
  }

  Widget _buildUrlField(String label, TextEditingController controller) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: SizedBox(
        height: 40, // Smaller height for URL fields
        child: TextFormField(
          controller: controller,
          style: AppTextStyles.inputText,
          decoration: AppInputDecorations.primaryInput(
            hintText: label,
          ).copyWith(border: null),
        ),
      ),
    );
  }
}
