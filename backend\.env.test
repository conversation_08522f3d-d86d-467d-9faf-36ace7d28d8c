
# 🧪 CURRENT: TEST ENVIRONMENT
# Switch to .env.production when ready for live deployment

# Server Configuration
PORT=4242
NODE_ENV=development
JWT_SECRET=test-jwt-secret-for-development-only

# Frontend URL (for local development)
FRONTEND_URL=http://localhost:3000

# Stripe TEST Keys (Safe for development)
STRIPE_SECRET_KEY=sk_test_51RuKq99sUmkxIP5cS3sVEMht0ZCNUbqXeAgfj9DqmVRHH8GeZRj9fv84Axuy956EdtNCjb3oDBunk4j4jutLgbNZ00TWns7PAd
STRIPE_PUBLISHABLE_KEY=pk_test_51RuKq99sUmkxIP5cvAP1sdoeRfQYZxFkQEqMsikMgjtkhSGkyir9x9BV0CIyhrFey7w9OP0mlootqmDTMJgXWdUu00bfDh1A61

# Stripe Webhook Secrets (will be updated after webhook setup)
STRIPE_WEBHOOK_SECRET=whsec_GYd5UWLMHjSfFrgOk3QmEfMzbnY7m6c8
STRIPE_CONNECT_WEBHOOK_SECRET=whsec_mbi1QP93GF29eqAlSewxAL0jPqf0dnzu

# Database (using local SQLite for testing)
DATABASE_PATH=./hashtagdollar_test.db