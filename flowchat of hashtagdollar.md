Updated Hashtag Dollar (#$) Flutter App Flow
1. Splash Screen

Logo: 🖤 with #$ inside (Purple)

Animation → Navigate to Login/Register

2. Login / Register Screen

Fields: Email, Password

Actions: Login, Forgot Password, Register

Google Translation enabled

3. Registration Screen

Fields: Full Name, Username (#$Username), Email, Password

Actions: Sign Up → Navigate to Fund Account

TOS / Privacy checkbox

Google Translation

4. Fund Account Screen

Purpose: Allow users to add funds to account

Payment Integration: Stripe

Minimum: $2.00

Display: Current balance, transaction history preview

Buttons:

Add Funds → Stripe

Back to Dashboard

Platforms: iOS, Android, Web

5. Dashboard / Home Screen

Header: Welcome, #$Username

Display: Current balance $X.XX

Buttons / Navigation:

Give $0.25 (main CTA)

Receive → List of hashtags linked to user

Fund Account → Navigate to Fund Account Screen

History → Navigate to Donation History

Promote #$Username → Share URL

Feed: Latest clickable hashtags from others

6. Give / Donate Flow

Input: Optional message

Select Receiver: Clickable #$Receiver (from feed or social media)

CTA: Give $0.25

Confirmation: Notification pops up:

#$GiverUsername 🖤 #$ReceiverUsername


Copy & share buttons

7. Receive Screen

Displays: Hashtags linked to user (#$JSUMS)

Donations received

CTA: Share your URL

8. History Screen

Displays:

Sent Donations (#$ReceiverUsername, amount, date)

Received Donations (#$GiverUsername, amount, date)

Filter by: Date / Sent / Received

Platforms: iOS, Android, Web

9. Account Settings

Options: Edit Profile, Delete Account, Privacy Policy, TOS, Support

10. Social Media Integration

Clickable #$Username

Deep linking to app → Donate or Register

Flow Diagram

[Splash Screen 🖤 #$] 
        |
        v
[Login Screen] ----> [Forgot Password?] 
        |
        v
   [Register Screen]
        |
        v
  [Fund Account Screen] <--- Optional top-up anytime
        |
        v
[Dashboard/Home Screen] ----> [Give $0.25] ---> [Select Receiver / Confirmation]
        |                               |
        |                               v
        |                       [Notification: #$Giver 🖤 #$Receiver]
        |
        +--> [Receive Screen] ---> [Share #$Username URL]
        |
        +--> [History Screen] ---> [Filter / View Donations]
        |
        +--> [Fund Account] ---> [Add Funds via Stripe]
        |
        +--> [Account Settings] ---> [Edit / Delete / Privacy / TOS / Support]



MORE UPDATES
flowchart TD
    A[Start Upgrade Process] --> B[UI/UX Setup]
    B --> B1[Add Containers for Mobile App & Website]
    B --> B2[Add Buttons: Search Users & Profile Upload]
    B --> B3[Add App Download Buttons on Login Page]

    B --> C[User Accounts]
    C --> C1[Generate Unique URL per Username]
    C1 --> C1a[Example: https://www.hashtagdollars.com/#$CuptoopiaCEO]
    C --> C2[Social Media Share = Short Username Only]
    C2 --> C2a[Example: #$CuptoopiaCEO]

    C --> D[Giving & Transactions]
    D --> D1[Blue Give $0.25 Button]
    D --> D2[Purple Give More Button (+$0.25)]
    D --> D3[Increase Amount Input Field (≤ Balance)]
    D --> D4[Transaction Fee Calculation via Stripe]
    D4 --> D4a[If $1.00 → Collect 10%]
    D4 --> D4b[If $2.00 → Collect 10% - $0.20 minimum]
    D --> D5[Credit Receiver’s Username]

    D --> E[Fund Management]
    E --> E1[Funds Stored with Stripe]
    E --> E2[Update Balance When Funds Added]
    E --> E3[Green Cash Out Button]
    E3 --> E4[Check Balance ≥ $5.00?]
    E4 -->|Yes| E5[Allow Payout]
    E4 -->|No| E6[Reject Payout & Notify User]

    E --> F[Deployment]
    F --> F1[Upload App to Google Play (.aab/.apk)]
    F --> F2[Deploy iOS App Store Version]
    F --> F3[Deploy Website]
    F --> F4[Re-record Video Demo (No Music)]

    F --> G[End]
