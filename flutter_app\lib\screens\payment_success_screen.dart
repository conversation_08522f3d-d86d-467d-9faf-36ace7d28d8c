// lib/screens/payment_success_screen.dart
import 'package:flutter/material.dart';
import '../services/api.dart';
import 'topup_screen.dart';

class PaymentSuccessScreen extends StatefulWidget {
  final int amountCents;
  const PaymentSuccessScreen({super.key, required this.amountCents});

  @override
  State<PaymentSuccessScreen> createState() => _PaymentSuccessScreenState();
}

class _PaymentSuccessScreenState extends State<PaymentSuccessScreen> {
  int currentBalance = 0;
  bool loadingBalance = true;

  @override
  void initState() {
    super.initState();
    // Add a small delay to ensure backend has processed the payment
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        _loadCurrentBalance();
      }
    });
  }

  Future<void> _loadCurrentBalance() async {
    try {
      final username = await Api.username();
      
      // If no username found, user might not be authenticated
      if (username == null || username.isEmpty || username == 'guest') {
        print('⚠️ No authenticated user found, redirecting to login');
        if (mounted) {
          Navigator.pushReplacementNamed(context, '/landing');
        }
        return;
      }
      
      final balance = await Api.getBalanceByUsername(username);
      if (mounted) {
        setState(() {
          currentBalance = balance ?? 0;
          loadingBalance = false;
        });
      }
    } catch (e) {
      print('❌ Error loading balance: $e');
      if (mounted) {
        setState(() => loadingBalance = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final amount = (widget.amountCents / 100.0).toStringAsFixed(2);
    final newBalance = (currentBalance / 100.0).toStringAsFixed(2);

    return Scaffold(
      appBar: AppBar(title: const Text('Payment Successful')),
      body: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 500),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text(
                  '🎉 Success!',
                  style: TextStyle(fontSize: 28, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                Text(
                  'Your balance has been topped up by \$$amount.',
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 18),
                ),
                const SizedBox(height: 16),
                if (loadingBalance)
                  const CircularProgressIndicator()
                else
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.green.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green.shade200),
                    ),
                    child: Column(
                      children: [
                        const Text(
                          '💰 Updated Balance',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '\$$newBalance',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextButton.icon(
                          onPressed: () {
                            setState(() => loadingBalance = true);
                            _loadCurrentBalance();
                          },
                          icon: const Icon(Icons.refresh, size: 16),
                          label: const Text('Refresh Balance'),
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.green.shade700,
                          ),
                        ),
                      ],
                    ),
                  ),
                const SizedBox(height: 28),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () async {
                      // Get logged-in username from API/SharedPreferences
                      final uname = await Api.username();
                      
                      if (uname == null || uname.isEmpty || uname == 'guest') {
                        // User not authenticated, go to login
                        if (context.mounted) {
                          Navigator.pushReplacementNamed(context, '/landing');
                        }
                        return;
                      }
                      
                      if (context.mounted) {
                        Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(
                            builder: (context) => TopupScreen(username: uname),
                          ),
                        );
                      }
                    },
                    child: const Text('Back to Top Up'),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
