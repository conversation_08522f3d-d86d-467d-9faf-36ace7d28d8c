const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const { createUser, findUserByEmail, verifyPassword, getUserById, deleteUserById } = require('../models');

const JWT_SECRET = process.env.JWT_SECRET || 'secret123';

router.post('/register', async (req, res) => {
  const { email, password, username, display_name } = req.body;
  if (!email || !password || !username) {
    return res.status(400).json({ message: 'Missing fields' });
  }

  const normalized = username.startsWith('#$') ? username : '#$' + username;

  try {
    // 1. Create Stripe Connected Account (for both donating + receiving)
    const account = await stripe.accounts.create({
      type: 'express',
      country: 'US',
      capabilities: {
        card_payments: { requested: true }, // to pay
        transfers: { requested: true },     // to receive
      },
      business_type: 'individual',
      email: email
    });

    // 2. Save user in DB
    const user = createUser({
      email,
      password,
      username: normalized,
      display_name,
      stripe_account_id: account.id,
      stripe_onboarded: 0
    });

    // 3. Token
    const token = jwt.sign({ id: user.id }, JWT_SECRET, { expiresIn: '30d' });

    res.json({
      token,
      user: {
        id: user.id,
        email: user.email,
        username: normalized,
        display_name,
        stripe_account_id: account.id,
        stripe_onboarded: 0
      }
    });

  } catch (err) {
    console.error(err.message);
    res.status(400).json({ message: 'Unable to create account - email or username may be taken' });
  }
});


router.post('/login', (req, res) => {
  const { email, password } = req.body;
  const u = verifyPassword(email, password);
  if (!u) return res.status(401).json({ message: 'Invalid credentials' });
  const token = jwt.sign({ id: u.id }, JWT_SECRET, { expiresIn: '30d' });
  res.json({ token, user: { id: u.id, email: u.email, username: u.username, display_name: u.display_name, balance: u.balance } });
});

// delete account (requires auth token) - client should call with token and user id check
router.post('/delete', (req, res) => {
  const auth = req.headers.authorization;
  if (!auth) return res.status(401).end();
  const token = auth.split(' ')[1];
  try {
    const payload = jwt.verify(token, JWT_SECRET);
    const uid = payload.id;
    deleteUserById(uid);
    res.json({ success: true });
  } catch (e) { res.status(401).end(); }
});

module.exports = router;