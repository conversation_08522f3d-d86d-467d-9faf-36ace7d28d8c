name: hashtag_dollars
description: Hashtag Dollars Micro Donation Platform
publish_to: "none"
version: 1.0.0

environment:
  sdk: ">=3.3.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  http: ^1.5.0
  shared_preferences: ^2.0.15
  url_launcher: ^6.3.2
  fluttertoast: ^8.0.9
  firebase_core: ^4.0.0
  firebase_analytics: ^12.0.0
  firebase_auth: ^6.0.1
  cloud_firestore: ^6.0.0
  firebase_storage: ^13.0.0
  flutter_stripe: ^11.0.0
  image_picker: ^0.8.7+5 # Mobile compatible
  image_picker_for_web: ^2.2.0 # Web-compatibles
  uni_links: ^0.5.1
  csv: ^5.0.0
  excel: ^2.0.7
  pdf: ^3.10.0
  printing: ^5.12.0
  cupertino_icons: ^1.0.5

  flutter_web_plugins:
    sdk: flutter
  webview_flutter: ^4.13.0
  video_player: ^2.8.2
  flutter_native_splash: ^2.3.10
  flutter_launcher_icons: ^0.13.1
  flutter_dotenv: ^5.1.0
  go_router: ^16.2.2
  cached_network_image: ^3.4.1
  share_plus: ^12.0.0
  flutter_svg: ^2.2.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.1

flutter:
  uses-material-design: true
  assets:
    - assets/logo.png
    - assets/.env

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/logo.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/logo.png"
    background_color: "#B76E79"
    theme_color: "#B76E79"
  windows:
    generate: true
    image_path: "assets/logo.png"
    icon_size: 48 # min:48, max:256, default: 48
