// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAXtirh1traszwTd8a83nDjkdH0du9YxUM',
    appId: '1:1013261542284:web:229e884e7826ee1daf6ca9',
    messagingSenderId: '1013261542284',
    projectId: 'hashtag-dollar',
    authDomain: 'hashtag-dollar.firebaseapp.com',
    storageBucket: 'hashtag-dollar.firebasestorage.app',
    measurementId: 'G-B2C2097571',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCIdthzSC8IZmBW4Vktqcbc6qrA4CYsZjU',
    appId: '1:1013261542284:android:26126a2859a93147af6ca9',
    messagingSenderId: '1013261542284',
    projectId: 'hashtag-dollar',
    storageBucket: 'hashtag-dollar.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBk-4yyjOJaEslLG2lFOjZEhp_fEjOvH54',
    appId: '1:1013261542284:ios:7b38be180f96435daf6ca9',
    messagingSenderId: '1013261542284',
    projectId: 'hashtag-dollar',
    storageBucket: 'hashtag-dollar.firebasestorage.app',
    iosBundleId: 'com.example.hashtagDollar',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBk-4yyjOJaEslLG2lFOjZEhp_fEjOvH54',
    appId: '1:1013261542284:ios:7b38be180f96435daf6ca9',
    messagingSenderId: '1013261542284',
    projectId: 'hashtag-dollar',
    storageBucket: 'hashtag-dollar.firebasestorage.app',
    iosBundleId: 'com.example.hashtagDollar',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAXtirh1traszwTd8a83nDjkdH0du9YxUM',
    appId: '1:1013261542284:web:50431fff450bfeecaf6ca9',
    messagingSenderId: '1013261542284',
    projectId: 'hashtag-dollar',
    authDomain: 'hashtag-dollar.firebaseapp.com',
    storageBucket: 'hashtag-dollar.firebasestorage.app',
    measurementId: 'G-H561ZF5Q13',
  );
}
