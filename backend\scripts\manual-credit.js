const { db, findUserByUsername } = require('../db');
const models = require('../models');

// Manual credit script for testing
// Usage: node scripts/manual-credit.js <username> <amount_in_cents>

const args = process.argv.slice(2);
if (args.length !== 2) {
  console.log('Usage: node scripts/manual-credit.js <username> <amount_in_cents>');
  process.exit(1);
}

const [username, amountStr] = args;
const amount = parseInt(amountStr);

if (isNaN(amount) || amount <= 0) {
  console.log('Amount must be a positive number');
  process.exit(1);
}

try {
  const user = findUserByUsername(username);
  if (!user) {
    console.log(`User ${username} not found`);
    process.exit(1);
  }

  console.log(`Current balance for ${username}: ${user.balance} cents`);

  models.creditUser(user.id, amount);

  const updatedUser = findUserByUsername(username);
  console.log(`✅ Successfully credited ${amount} cents to ${username}`);
  console.log(`New balance: ${updatedUser.balance} cents`);

  // Create transaction record
  models.createTransaction({
    from: null,
    to: user.id,
    amount: amount,
    type: 'topup',
    note: `Manual credit for testing`
  });

  console.log('✅ Transaction record created');

} catch (error) {
  console.error('Error:', error.message);
  process.exit(1);
}