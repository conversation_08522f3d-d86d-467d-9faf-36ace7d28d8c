const { db } = require('./db');
const { nanoid } = require('nanoid');
const bcrypt = require('bcryptjs');

module.exports = {
  // ------------------------
  // User Methods
  // ------------------------
  createUser: ({ email, password, username, display_name }) => {
    const id = nanoid();
    const ts = Date.now();
    const hashed = bcrypt.hashSync(password, 10);
    const stmt = db.prepare(
      'INSERT INTO users (id,email,password,username,display_name,created_at) VALUES (?,?,?,?,?,?)'
    );
    stmt.run(id, email, hashed, username, display_name, ts);
    return { id, email, username, display_name };
  },

  findUserByEmail: (email) =>
    db.prepare('SELECT * FROM users WHERE email=?').get(email),

  findUserByUsername: (username) =>
    db.prepare('SELECT * FROM users WHERE username=?').get(username),

  getUserById: (id) =>
    db.prepare(
      'SELECT id,email,username,display_name,balance,created_at FROM users WHERE id=?'
    ).get(id),

  getAllUsers: () =>
    db.prepare('SELECT id,username,display_name,balance FROM users ORDER BY created_at DESC').all(),

  verifyPassword: (email, password) => {
    const u = db.prepare('SELECT * FROM users WHERE email=?').get(email);
    if (!u) return false;
    return bcrypt.compareSync(password, u.password) ? u : false;
  },

  creditUser: (userId, cents) => {
    db.prepare('UPDATE users SET balance = balance + ? WHERE id = ?').run(cents, userId);
  },

  transfer: (fromId, toId, cents) => {
    const id = nanoid();
    const ts = Date.now();
    const t = db.transaction(() => {
      const from = db.prepare('SELECT balance FROM users WHERE id = ?').get(fromId);
      if (!from || from.balance < cents) throw new Error('Insufficient funds');
      db.prepare('UPDATE users SET balance = balance - ? WHERE id = ?').run(cents, fromId);
      db.prepare('UPDATE users SET balance = balance + ? WHERE id = ?').run(cents, toId);
      db.prepare(
        'INSERT INTO transactions (id,from_user,to_user,amount,type,created_at) VALUES (?,?,?,?,?,?)'
      ).run(id, fromId, toId, cents, 'donation', ts);
    });
    t();
    return id;
  },

  addPlatformFunds: (cents) =>
    db.prepare('UPDATE platform SET balance = balance + ? WHERE id = 1').run(cents),

  createTransaction: ({ from, to, amount, type, note }) => {
    const id = nanoid();
    db.prepare(
      'INSERT INTO transactions (id,from_user,to_user,amount,type,note,created_at) VALUES (?,?,?,?,?,?,?)'
    ).run(id, from || null, to || null, amount, type, note || null, Date.now());
    return id;
  },

  deleteUserById: (id) => {
    const t = db.transaction(() => {
      db.prepare('DELETE FROM transactions WHERE from_user = ? OR to_user = ?').run(id, id);
      db.prepare('DELETE FROM users WHERE id = ?').run(id);
    });
    t();
  },

  // ------------------------
  // Payment Methods
  // ------------------------
  createPayment: ({ userId, amount, currency, method, status, reference, stripePaymentIntentId }) => {
    const id = nanoid();
    const ts = Date.now();
    db.prepare(
      `INSERT INTO payments 
      (id, user_id, amount, currency, method, status, reference, stripe_payment_intent_id, created_at) 
      VALUES (?,?,?,?,?,?,?,?,?)`
    ).run(
      id,
      userId,
      amount,
      currency,
      method,
      status || 'pending',
      reference || null,
      stripePaymentIntentId || null,
      ts
    );
    return { id, userId, amount, currency, method, status, reference, stripePaymentIntentId, created_at: ts };
  },

  getPaymentsByUser: (userId) => {
    return db.prepare('SELECT * FROM payments WHERE user_id = ? ORDER BY created_at DESC').all(userId);
  },

  getPaymentById: (id) => {
    return db.prepare('SELECT * FROM payments WHERE id = ?').get(id);
  },

  // ✅ NEW: get payment by reference (e.g. Stripe session.id)
  getPaymentByReference: (reference) => {
    return db.prepare('SELECT * FROM payments WHERE reference = ?').get(reference);
  },

  updatePaymentStatus: (id, status) => {
    db.prepare('UPDATE payments SET status = ? WHERE id = ?').run(status, id);
    return db.prepare('SELECT * FROM payments WHERE id = ?').get(id);
  },

  // ✅ NEW: update payment status by reference
  updatePaymentStatusByReference: (reference, status) => {
    db.prepare('UPDATE payments SET status = ? WHERE reference = ?').run(status, reference);
    return db.prepare('SELECT * FROM payments WHERE reference = ?').get(reference);
  },

  // ✅ Transactions for a user (donations + topups)
  getTransactionsByUser: (userId) => {
    return db.prepare(
      `SELECT id, from_user, to_user, amount, type, note, created_at
       FROM transactions
       WHERE from_user = ? OR to_user = ?
       ORDER BY created_at DESC`
    ).all(userId, userId);
  }
};
