import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_web_plugins/flutter_web_plugins.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'services/api.dart';
import 'screens/login_screen.dart';
import 'screens/register_screen.dart';
import 'screens/home_screen.dart';
import 'screens/topup_screen.dart';
import 'screens/profile_screen.dart';
import 'screens/userprofile_screen.dart'; // Added import
import 'screens/public_profile_screen.dart';
import 'screens/personal_profile_screen.dart';
import 'screens/payment_success_screen.dart';
import 'screens/landing_screen.dart';
import 'screens/privacy_screen.dart';
import 'screens/terms_screen.dart';
import 'screens/contact_screen.dart';
import 'services/translator_service.dart';

void main() async {
  // ✅ Load environment variables
  await dotenv.load(fileName: "assets/.env");

  // ✅ Get server URL from .env file
  final serverUrl = dotenv.env['SERVER_URL'] ?? 'http://localhost:4242';
  Api.base = serverUrl;
  print('✅ Server URL: $serverUrl');

  // Use hash strategy
  setUrlStrategy(const HashUrlStrategy());
  runApp(const HashtagDollarApp());
}

class HashtagDollarApp extends StatefulWidget {
  const HashtagDollarApp({super.key});

  @override
  State<HashtagDollarApp> createState() => _HashtagDollarAppState();
}

class _HashtagDollarAppState extends State<HashtagDollarApp> {
  final GlobalKey<NavigatorState> _navKey = GlobalKey<NavigatorState>();
  int? _paymentAmount;
  bool _checkedStripe = false;
  final TranslatorService _translator = TranslatorService();
  String selectedLanguage = 'en';
  bool _isAuthenticated = false;
  bool _authChecked = false;
  String _loggedInUsername = 'guest';

  @override
  void initState() {
    super.initState();
    _checkAuthentication();
    if (kIsWeb) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _handleWebStripeSuccess();
      });
    }
  }

  Future<void> _checkAuthentication() async {
    try {
      final token = await Api.token();
      final username = await Api.username();

      print(
          '🔍 Auth Check: Token exists: ${token != null && token.isNotEmpty}');
      print(
          '🔍 Auth Check: Username exists: ${username != null && username.isNotEmpty}');

      if (token != null &&
          username != null &&
          token.isNotEmpty &&
          username.isNotEmpty) {
        print('✅ Auth Check: User is authenticated as $username');
        setState(() {
          _isAuthenticated = true;
          _loggedInUsername = username;
          _authChecked = true;
        });
      } else {
        print('❌ Auth Check: User is not authenticated');
        setState(() {
          _isAuthenticated = false;
          _loggedInUsername = 'guest';
          _authChecked = true;
        });
      }
    } catch (e) {
      print('🚨 Auth Check: Error during authentication check: $e');
      setState(() {
        _isAuthenticated = false;
        _loggedInUsername = 'guest';
        _authChecked = true;
      });
    }
  }

  Future<void> _handleWebStripeSuccess() async {
    Uri uri = Uri.base;
    if (uri.fragment.isNotEmpty && uri.fragment.startsWith('/')) {
      uri = Uri.parse(uri.fragment);
    }
    final sessionId = uri.queryParameters['session_id'];
    if (sessionId != null && !_checkedStripe) {
      _checkedStripe = true;
      final amount = await Api.getAmountOnSuccess(sessionId) ?? 0;
      setState(() => _paymentAmount = amount);

      Future.microtask(() {
        _navKey.currentState?.pushReplacementNamed(
          '/success',
          arguments: {'amount': amount, 'sessionId': sessionId},
        );
      });
    }
  }

  void _changeLanguage(String lang) {
    setState(() => selectedLanguage = lang);
  }

  Widget _wrapWithAppShell(Widget child, {bool showContactUs = true}) {
    return AppShell(
      translator: _translator,
      selectedLanguage: selectedLanguage,
      onLanguageChange: _changeLanguage,
      onLogout: _handleLogout,
      onLogoClick: _handleLogoClick,
      isAuthenticated: _isAuthenticated,
      showContactUs: showContactUs,
      child: child,
    );
  }

  Future<void> _handleLogoClick() async {
    // Check if user has stored credentials and auto-login
    final token = await Api.token();
    final username = await Api.username();

    if (token != null &&
        username != null &&
        token.isNotEmpty &&
        username.isNotEmpty) {
      // User has stored credentials, navigate to home
      if (!_isAuthenticated) {
        setState(() {
          _isAuthenticated = true;
          _loggedInUsername = username;
        });
      }
      _navKey.currentState?.pushNamedAndRemoveUntil('/home', (route) => false,
          arguments: username);
    } else {
      // No stored credentials, go to landing page
      _navKey.currentState
          ?.pushNamedAndRemoveUntil('/landing', (route) => false);
    }
  }

  Future<void> _handleLogout() async {
    await Api.logout();
    setState(() {
      _isAuthenticated = false;
      _loggedInUsername = 'guest';
    });
    _navKey.currentState?.pushReplacementNamed('/login');
  }

  Future<void> _updateAuthState() async {
    try {
      final token = await Api.token();
      final username = await Api.username();

      if (token != null &&
          username != null &&
          token.isNotEmpty &&
          username.isNotEmpty) {
        setState(() {
          _isAuthenticated = true;
          _loggedInUsername = username;
        });
      }
    } catch (e) {
      debugPrint('Error updating auth state: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    Uri uri = Uri.base;
    if (uri.fragment.isNotEmpty && uri.fragment.startsWith('/')) {
      uri = Uri.parse(uri.fragment);
    }
    final hasStripeSession = uri.queryParameters['session_id'] != null;

    // Show loading screen while checking authentication
    if (!_authChecked) {
      return MaterialApp(
        title: 'Hashtag Dollar #\$',
        theme: ThemeData(primarySwatch: Colors.purple),
        debugShowCheckedModeBanner: false,
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset('assets/logo.png', height: 80),
                const SizedBox(height: 20),
                const CircularProgressIndicator(),
                const SizedBox(height: 20),
                const Text('Loading...'),
              ],
            ),
          ),
        ),
      );
    }

    return MaterialApp(
      navigatorKey: _navKey,
      title: 'Hashtag Dollars #\$',
      theme: ThemeData(primarySwatch: Colors.purple),
      debugShowCheckedModeBanner: false,
      initialRoute: hasStripeSession
          ? '/success'
          : (_isAuthenticated ? '/home' : '/landing'),
      onGenerateRoute: (settings) {
        switch (settings.name) {
          case '/landing':
            return MaterialPageRoute(
              builder: (_) => const LandingScreen(),
            );

          case '/privacy':
            return MaterialPageRoute(
              builder: (_) => const PrivacyScreen(),
            );

          case '/terms':
            return MaterialPageRoute(
              builder: (_) => const TermsScreen(),
            );

          case '/contactus':
            return MaterialPageRoute(
              builder: (_) => const ContactScreen(),
            );

          case '/login':
            return MaterialPageRoute(
              builder: (_) => const LoginScreen(),
            );

          case '/register':
            return MaterialPageRoute(
              builder: (_) => const RegisterScreen(),
            );

          case '/home':
            // Update auth state when navigating to home
            _updateAuthState();
            final username = settings.arguments as String? ?? _loggedInUsername;
            return MaterialPageRoute(
              builder: (_) =>
                  _wrapWithAppShell(HomeScreen(loggedInUsername: username)),
            );

          case '/profile':
            final args = settings.arguments as Map<String, String>? ?? {};
            final fromUsername = args['fromUsername'] ?? 'guest';
            final toUsername = args['toUsername'] ?? 'demoUser';
            return MaterialPageRoute(
              builder: (_) => _wrapWithAppShell(ProfileScreen(
                fromUsername: fromUsername,
                toUsername: toUsername,
              )),
            );

          case '/userprofile': // Profile Edit Screen
            _updateAuthState();
            final username = settings.arguments as String? ?? _loggedInUsername;
            return MaterialPageRoute(
              builder: (_) =>
                  _wrapWithAppShell(UserProfileScreen(username: username)),
            );

          case '/personal-profile': // Personal Profile View
            _updateAuthState();
            final username = settings.arguments as String? ?? _loggedInUsername;
            return MaterialPageRoute(
              builder: (_) =>
                  _wrapWithAppShell(PersonalProfileScreen(username: username)),
            );

          case '/public-profile': // Public Profile View
            final args = settings.arguments as Map<String, String>? ?? {};
            final username = args['username'] ?? 'guest';
            final viewerUsername = args['viewerUsername'];
            return MaterialPageRoute(
              builder: (_) => _wrapWithAppShell(PublicProfileScreen(
                username: username,
                viewerUsername: viewerUsername,
              )),
            );

          case '/topup':
            final username = settings.arguments as String? ?? 'demoUser';
            return MaterialPageRoute(
              builder: (_) => _wrapWithAppShell(TopupScreen(username: username),
                  showContactUs: false),
            );

          case '/success':
            final amount = int.tryParse(uri.queryParameters['amount'] ?? '0') ??
                (settings.arguments as Map?)?['amount'] ??
                _paymentAmount ??
                0;
            final sessionId = uri.queryParameters['session_id'] ??
                (settings.arguments as Map?)?['sessionId'] ??
                '';

            // Update balance via success callback (fallback if webhook fails)
            Future.microtask(() async {
              try {
                final username = await Api.username();

                // If no authenticated user, skip balance update
                if (username == null ||
                    username.isEmpty ||
                    username == 'guest') {
                  debugPrint(
                      '⚠️ No authenticated user found in success callback');
                  return;
                }

                debugPrint(
                    '🔄 Success callback: Updating balance for $username, amount: $amount cents, session: $sessionId');

                // Call the success endpoint which will update balance if not already updated
                final res = await http.get(
                  Uri.parse(
                      '${Api.baseUrl}/api/payments/success?session_id=$sessionId'),
                  headers: {'Content-Type': 'application/json'},
                );

                debugPrint('📡 Success callback response: ${res.statusCode}');
                if (res.statusCode == 200) {
                  debugPrint('✅ Balance updated via success callback');
                } else {
                  debugPrint('❌ Success callback failed: ${res.body}');
                }
              } catch (e) {
                debugPrint('❌ Error in success callback: $e');
              }
            });

            return MaterialPageRoute(
              builder: (_) =>
                  _wrapWithAppShell(PaymentSuccessScreen(amountCents: amount)),
            );

          default:
            return MaterialPageRoute(
              builder: (_) => _wrapWithAppShell(const LoginScreen()),
            );
        }
      },
    );
  }
}

/// ------------------------
/// APP SHELL WIDGET
/// Wraps every page with top navbar, translation & logo
/// ------------------------
class AppShell extends StatefulWidget {
  final Widget child;
  final TranslatorService translator;
  final String selectedLanguage;
  final Function(String) onLanguageChange;
  final Function()? onLogout;
  final Function()? onLogoClick;
  final bool isAuthenticated;
  final bool showContactUs;

  const AppShell({
    super.key,
    required this.child,
    required this.translator,
    required this.selectedLanguage,
    required this.onLanguageChange,
    this.onLogout,
    this.onLogoClick,
    this.isAuthenticated = false,
    this.showContactUs = true,
  });

  @override
  State<AppShell> createState() => _AppShellState();
}

class _AppShellState extends State<AppShell> {
  Map<String, String> translations = {};

  @override
  void initState() {
    super.initState();
    _translateUI();
  }

  Future<void> _translateUI() async {
    Map<String, String> originalTexts = {
      'about': 'About Us',
      'contact': 'Contact Us',
      'privacy': 'Privacy Policy',
      'ads': 'Ads',
    };

    Map<String, String> translated = {};
    for (var key in originalTexts.keys) {
      translated[key] = await widget.translator
          .translate(originalTexts[key]!, widget.selectedLanguage);
    }

    setState(() => translations = translated);
  }

  @override
  void didUpdateWidget(covariant AppShell oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedLanguage != widget.selectedLanguage) {
      _translateUI();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            GestureDetector(
              onTap: widget.onLogoClick,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.asset('assets/logo.png', height: 40),
                  const SizedBox(width: 8),
                  const Text('Hashtag Dollars #\$'),
                ],
              ),
            ),
            const Spacer(),
            if (widget.showContactUs) ...[
              TextButton(
                  onPressed: () {
                    Navigator.pushNamed(context, '/contactus');
                  },
                  child: Text(translations['contact'] ?? 'Contact Us',
                      style: const TextStyle(color: Colors.black))),
            ],
            TextButton(
                onPressed: () {
                  Navigator.pushNamed(context, '/privacy');
                },
                child: const Text('Privacy Policy',
                    style: TextStyle(color: Colors.black))),
            if (widget.isAuthenticated && widget.onLogout != null) ...[
              const SizedBox(width: 8),
              TextButton(
                onPressed: widget.onLogout,
                child:
                    const Text('Logout', style: TextStyle(color: Colors.black)),
              ),
            ],
            const SizedBox(width: 8),
            DropdownButton<String>(
              dropdownColor: Colors.purple,
              value: widget.selectedLanguage,
              items: const [
                DropdownMenuItem(value: 'en', child: Text('English')),
                DropdownMenuItem(value: 'es', child: Text('Spanish')),
                DropdownMenuItem(value: 'fr', child: Text('French')),
                DropdownMenuItem(value: 'de', child: Text('German')),
              ],
              onChanged: (val) {
                if (val != null) widget.onLanguageChange(val);
              },
            ),
          ],
        ),
      ),
      body: widget.child,
    );
  }
}
