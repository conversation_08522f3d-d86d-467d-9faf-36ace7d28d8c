import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'constants/theme_constants.dart';
import 'services/api.dart';
import 'services/balance_provider.dart';
import 'screens/login_screen.dart';
import 'screens/register_screen.dart';
import 'screens/forgot_password_screen.dart';
import 'screens/home_screen.dart';
import 'screens/topup_screen.dart';
import 'screens/give_cashOut_screen.dart';
import 'screens/userprofile_screen.dart'; // Added import
import 'screens/userprofile_view_screen.dart'; // Added import
import 'screens/social_profile_screen.dart'; // Added import
import 'screens/payment_success_screen.dart';
import 'screens/landing_screen.dart';
import 'screens/privacy_screen.dart';
import 'screens/terms_screen.dart';
import 'screens/contact_screen.dart';
import 'services/translator_service.dart';

void main() async {
  // ✅ Load environment variables
  await dotenv.load(fileName: "assets/.env");

  // ✅ Get server URL from .env file
  final serverUrl = dotenv.env['SERVER_URL'] ?? 'http://localhost:4242';
  Api.base = serverUrl;
  // <========== logs were here, add again if required for debug =========>

  // Note: Hash URL strategy setup removed to avoid mobile compilation issues
  // The app will use default URL strategy on mobile
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => BalanceProvider()),
      ],
      child: const HashtagDollarApp(),
    ),
  );
}

class HashtagDollarApp extends StatefulWidget {
  const HashtagDollarApp({super.key});

  @override
  State<HashtagDollarApp> createState() => _HashtagDollarAppState();
}

class _HashtagDollarAppState extends State<HashtagDollarApp> {
  final GlobalKey<NavigatorState> _navKey = GlobalKey<NavigatorState>();
  int? _paymentAmount;
  bool _checkedStripe = false;
  final TranslatorService _translator = TranslatorService();
  String selectedLanguage = 'en';
  bool _isAuthenticated = false;
  bool _authChecked = false;
  String _loggedInUsername = 'guest';

  @override
  void initState() {
    super.initState();
    _checkAuthentication();
    if (kIsWeb) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _handleWebStripeSuccess();
      });
    }
  }

  Future<void> _checkAuthentication() async {
    try {
      // Check session validity first
      final isSessionValid = await Api.isSessionValid();

      if (!isSessionValid) {
        // <========== logs were here, add again if required for debug =========>
        setState(() {
          _isAuthenticated = false;
          _authChecked = true;
        });
        return;
      }

      // Get authentication data
      final token = await Api.token();
      final username = await Api.username();

      print(
          '🔐 Main: Checking auth - Token: ${token != null ? 'present' : 'null'}, Username: $username');

      if (token != null &&
          username != null &&
          token.isNotEmpty &&
          username.isNotEmpty) {
        setState(() {
          _isAuthenticated = true;
          _loggedInUsername = username;
          _authChecked = true;
        });
        // <========== logs were here, add again if required for debug =========>
      } else {
        setState(() {
          _isAuthenticated = false;
          _authChecked = true;
        });
        // <========== logs were here, add again if required for debug =========>
      }
    } catch (e) {
      // <========== logs were here, add again if required for debug =========>
      setState(() {
        _isAuthenticated = false;
        _authChecked = true;
      });
    }
  }

  Future<void> _handleWebStripeSuccess() async {
    Uri uri = Uri.base;
    if (uri.fragment.isNotEmpty && uri.fragment.startsWith('/')) {
      uri = Uri.parse(uri.fragment);
    }
    final sessionId = uri.queryParameters['session_id'];
    if (sessionId != null && !_checkedStripe) {
      _checkedStripe = true;
      final amount = await Api.getAmountOnSuccess(sessionId) ?? 0;
      setState(() => _paymentAmount = amount);

      Future.microtask(() {
        _navKey.currentState?.pushReplacementNamed(
          '/success',
          arguments: {'amount': amount, 'sessionId': sessionId},
        );
      });
    }
  }

  void _changeLanguage(String lang) {
    setState(() => selectedLanguage = lang);
  }

  Widget _wrapWithAppShell(Widget child,
      {String title = 'Hashtag Dollar #\$'}) {
    return AppShell(
      title: title,
      loggedInUsername: _loggedInUsername,
      translator: _translator,
      selectedLanguage: selectedLanguage,
      onLanguageChange: _changeLanguage,
      onLogout: _handleLogout,
      isAuthenticated: _isAuthenticated,
      child: child,
    );
  }

  Future<void> _handleLogout() async {
    await Api.logout();
    setState(() {
      _isAuthenticated = false;
      _loggedInUsername = '';
    });
    _navKey.currentState?.pushReplacementNamed('/login');
  }

  Future<void> _updateAuthState() async {
    try {
      final token = await Api.token();
      final username = await Api.username();

      if (token != null &&
          username != null &&
          token.isNotEmpty &&
          username.isNotEmpty) {
        setState(() {
          _isAuthenticated = true;
          _loggedInUsername = username;
        });

        // Initialize balance provider for the authenticated user
        WidgetsBinding.instance.addPostFrameCallback((_) {
          final context = _navKey.currentState?.context;
          if (context != null) {
            final balanceProvider =
                Provider.of<BalanceProvider>(context, listen: false);
            balanceProvider.initialize(username);
          }
        });
      }
    } catch (e) {
      debugPrint('Error updating auth state: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    Uri uri = Uri.base;
    if (uri.fragment.isNotEmpty && uri.fragment.startsWith('/')) {
      uri = Uri.parse(uri.fragment);
    }
    final hasStripeSession = uri.queryParameters['session_id'] != null;

    // Show loading screen while checking authentication
    if (!_authChecked) {
      return MaterialApp(
        title: 'Hashtag Dollar #\$',
        theme: ThemeData(primarySwatch: Colors.purple),
        debugShowCheckedModeBanner: false,
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.asset('assets/logo.png', height: 50),
                    // Title
                    const Text(
                      'Hashtag Dollars',
                      style: AppTextStyles.title,
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                const CircularProgressIndicator(),
                const SizedBox(height: 20),
                const Text('Loading...'),
              ],
            ),
          ),
        ),
      );
    }

    return MaterialApp(
      navigatorKey: _navKey,
      title: 'Hashtag Dollars #\$',
      theme: ThemeData(primarySwatch: Colors.purple),
      debugShowCheckedModeBanner: false,
      initialRoute: () {
        if (hasStripeSession) return '/success';

        // Check for social profile links
        Uri uri = Uri.base;
        if (uri.fragment.isNotEmpty && uri.fragment.startsWith('/')) {
          uri = Uri.parse(uri.fragment);
        }
        final hasSocialProfile = uri.path == '/social-profile' &&
            uri.queryParameters['username'] != null;

        if (hasSocialProfile) {
          return '/social-profile';
        }

        // Check for direct URL access to static pages
        if (uri.path == '/privacy') return '/privacy';
        if (uri.path == '/terms') return '/terms';
        if (uri.path == '/contactus') return '/contactus';
        if (uri.path == '/stripe-return') return '/stripe-return';
        if (uri.path == '/stripe-refresh') return '/stripe-refresh';

        return _isAuthenticated ? '/home' : '/landing';
      }(),
      onGenerateRoute: (settings) {
        switch (settings.name) {
          case '/landing':
            return MaterialPageRoute(
              builder: (_) => const LandingScreen(),
            );

          case '/privacy':
            return MaterialPageRoute(
              builder: (_) => const PrivacyScreen(),
            );

          case '/terms':
            return MaterialPageRoute(
              builder: (_) => const TermsScreen(),
            );

          case '/contactus':
            return MaterialPageRoute(
              builder: (_) => const ContactScreen(),
            );

          case '/stripe-return':
            // Handle Stripe onboarding completion
            WidgetsBinding.instance.addPostFrameCallback((_) {
              Navigator.of(context).pop({'success': true});
            });
            return MaterialPageRoute(
              builder: (_) => const Scaffold(
                body: Center(
                  child: Text('Verification completed! Returning to app...'),
                ),
              ),
            );

          case '/stripe-refresh':
            // Handle Stripe onboarding refresh/cancel
            WidgetsBinding.instance.addPostFrameCallback((_) {
              Navigator.of(context).pop({'success': false});
            });
            return MaterialPageRoute(
              builder: (_) => const Scaffold(
                body: Center(
                  child: Text('Verification cancelled. Returning to app...'),
                ),
              ),
            );

          case '/login':
            return MaterialPageRoute(
              builder: (_) => const LoginScreen(),
            );

          case '/register':
            return MaterialPageRoute(
              builder: (_) => const RegisterScreen(),
            );

          case '/forgot-password':
            return MaterialPageRoute(
              builder: (_) => const ForgotPasswordScreen(),
            );

          case '/home':
            // Update auth state when navigating to home
            _updateAuthState();
            final username = settings.arguments as String? ?? _loggedInUsername;
            return MaterialPageRoute(
              builder: (_) =>
                  _wrapWithAppShell(HomeScreen(loggedInUsername: username)),
            );

          case '/profile':
            final args = settings.arguments as Map<String, String>? ?? {};
            final fromUsername = args['fromUsername'] ?? 'Unknown';
            final toUsername = args['toUsername'] ?? 'Unknown';
            return MaterialPageRoute(
              builder: (_) => _wrapWithAppShell(
                  GiveCashOutScreen(
                    fromUsername: fromUsername,
                    toUsername: toUsername,
                  ),
                  title: 'Profile'),
            );

          case '/userprofile': // <-- New route
            // Update auth state when accessing profile
            _updateAuthState();
            final username = settings.arguments as String? ?? _loggedInUsername;
            return MaterialPageRoute(
              builder: (_) =>
                  _wrapWithAppShell(UserProfileScreen(username: username)),
            );

          case '/userprofileview': // <-- View profile route
            // Update auth state when accessing profile
            _updateAuthState();
            final username = settings.arguments as String? ?? _loggedInUsername;
            return MaterialPageRoute(
              builder: (_) =>
                  _wrapWithAppShell(UserProfileView(username: username)),
            );

          case '/social-profile':
            // Handle shareable social profile links
            // Extract username from URL parameters or route arguments
            Uri uri = Uri.base;
            if (uri.fragment.isNotEmpty && uri.fragment.startsWith('/')) {
              uri = Uri.parse(uri.fragment);
            }
            final rawUsername = Uri.decodeComponent(
                uri.queryParameters['username'] ??
                    (settings.arguments as Map?)?['username'] as String? ??
                    'guest');

            // Add #$ prefix if not present (for clean sharing URLs)
            final usernameFromUrl =
                rawUsername.startsWith('#\$') ? rawUsername : "#\$$rawUsername";

            return MaterialPageRoute(
              builder: (_) => _wrapWithAppShell(
                SocialProfileScreen(
                  fromUsername: _isAuthenticated ? _loggedInUsername : '',
                  toUsername: usernameFromUrl,
                ),
                title: 'Social Profile',
              ),
            );

          case '/topup':
            final username = settings.arguments as String? ?? 'demoUser';
            return MaterialPageRoute(
              builder: (_) => _wrapWithAppShell(
                TopupScreen(username: username),
              ),
            );

          case '/success':
            final amount = int.tryParse(uri.queryParameters['amount'] ?? '0') ??
                (settings.arguments as Map?)?['amount'] ??
                _paymentAmount ??
                0;
            final sessionId = uri.queryParameters['session_id'] ??
                (settings.arguments as Map?)?['sessionId'] ??
                '';

            // Update balance via success callback (fallback if webhook fails)
            Future.microtask(() async {
              try {
                final username = await Api.username();

                // If no authenticated user, skip balance update
                if (username == null ||
                    username.isEmpty ||
                    username == 'guest') {
                  debugPrint(
                      '⚠️ No authenticated user found in success callback');
                  return;
                }

                debugPrint(
                    '🔄 Success callback: Updating balance for $username, amount: $amount cents, session: $sessionId');

                // Call the success endpoint which will update balance if not already updated
                final res = await http.get(
                  Uri.parse(
                      '${Api.baseUrl}/api/payments/success?session_id=$sessionId'),
                  headers: {'Content-Type': 'application/json'},
                );

                debugPrint('📡 Success callback response: ${res.statusCode}');
                if (res.statusCode == 200) {
                  debugPrint('✅ Balance updated via success callback');
                } else {
                  debugPrint('❌ Success callback failed: ${res.body}');
                }
              } catch (e) {
                debugPrint('❌ Error in success callback: $e');
              }
            });

            return MaterialPageRoute(
              builder: (_) =>
                  _wrapWithAppShell(PaymentSuccessScreen(amountCents: amount)),
            );

          default:
            return MaterialPageRoute(
              builder: (_) => _wrapWithAppShell(const LoginScreen()),
            );
        }
      },
    );
  }
}

/// ------------------------
/// APP SHELL WIDGET
/// Wraps every page with top navbar, translation & logo
/// ------------------------
class AppShell extends StatefulWidget {
  final Widget child;
  final TranslatorService translator;
  final String selectedLanguage;
  final Function(String) onLanguageChange;
  final Function()? onLogout;
  final bool isAuthenticated;
  final bool showContactUs;
  final String loggedInUsername;
  final String title;

  const AppShell({
    super.key,
    required this.child,
    required this.translator,
    required this.selectedLanguage,
    required this.onLanguageChange,
    this.onLogout,
    this.isAuthenticated = false,
    this.showContactUs = true,
    this.loggedInUsername = '',
    required this.title,
  });

  @override
  State<AppShell> createState() => _AppShellState();
}

class _AppShellState extends State<AppShell> {
  Map<String, String> translations = {};

  @override
  void initState() {
    super.initState();
  }

  // Auto-login function for logo click
  Future<void> _handleLogoClick(BuildContext context) async {
    try {
      final isSessionValid = await Api.isSessionValid();
      if (isSessionValid) {
        final username = await Api.username();
        if (username != null && username.isNotEmpty) {
          // <========== logs were here, add again if required for debug =========>
          // Navigate to home if not already there
          if (ModalRoute.of(context)?.settings.name != '/home') {
            Navigator.pushReplacementNamed(context, '/home',
                arguments: username);
          }
          return;
        }
      }
      // If no valid session, navigate to landing page
      if (ModalRoute.of(context)?.settings.name != '/landing') {
        Navigator.pushReplacementNamed(context, '/landing');
      }
    } catch (e) {
      // <========== logs were here, add again if required for debug =========>
      // Fallback to landing page
      Navigator.pushReplacementNamed(context, '/landing');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar: MainAppBar(
      //   loggedInUsername: widget.loggedInUsername,
      //   headerTitle: widget.title,
      // ),
      body: widget.child,
    );
  }
}
