const Database = require('better-sqlite3');
const path = require('path');

// Initialize database
const db = new Database(path.join(__dirname, 'hashtagdollar.db'));

console.log('🗑️ Deleting test accounts #$user1 through #$user10...');

// Delete test users
const deleteUser = db.prepare('DELETE FROM users WHERE username LIKE ?');
const deletePayments = db.prepare('DELETE FROM payments WHERE username LIKE ?');
const deletePaymentHistory = db.prepare('DELETE FROM payment_history WHERE username LIKE ?');
const deleteDonations = db.prepare('DELETE FROM donations WHERE fromUsername LIKE ? OR toUsername LIKE ?');
const deleteDonationHistory = db.prepare('DELETE FROM donation_history WHERE from_username LIKE ? OR to_username LIKE ?');
const deleteTransactions = db.prepare('DELETE FROM transactions WHERE from_user LIKE ? OR to_user LIKE ?');
const deleteFavorites = db.prepare('DELETE FROM favorite_givers WHERE user_id IN (SELECT id FROM users WHERE username LIKE ?) OR giver_id IN (SELECT id FROM users WHERE username LIKE ?)');

// Delete favorite givers first (due to foreign key constraints)
deleteFavorites.run('#$user%', '#$user%');

// Delete transactions and donations
deleteTransactions.run('#$user%', '#$user%');
deleteDonations.run('#$user%', '#$user%');
deleteDonationHistory.run('#$user%', '#$user%');

// Delete payment records
deletePaymentHistory.run('#$user%');
deletePayments.run('#$user%');

// Delete users
const result = deleteUser.run('#$user%');

console.log(`✅ Deleted ${result.changes} test user accounts`);

// Also delete any accounts with test-added funds (balance > 1000 cents = $10.00)
console.log('🗑️ Deleting accounts with test-added funds (balance > $10.00)...');

const deleteHighBalanceUsers = db.prepare('DELETE FROM users WHERE balance > 1000');
const deleteHighBalancePayments = db.prepare('DELETE FROM payments WHERE amount > 1000');
const deleteHighBalancePaymentHistory = db.prepare('DELETE FROM payment_history WHERE amount > 1000');

// Delete related records for high balance users
const highBalanceUsers = db.prepare('SELECT username FROM users WHERE balance > 1000').all();
for (const user of highBalanceUsers) {
  deleteFavorites.run(user.username, user.username);
  deleteTransactions.run(user.username, user.username);
  deleteDonations.run(user.username, user.username);
  deleteDonationHistory.run(user.username, user.username);
  deletePaymentHistory.run(user.username);
  deletePayments.run(user.username);
}

const highBalanceResult = deleteHighBalanceUsers.run();
console.log(`✅ Deleted ${highBalanceResult.changes} accounts with test-added funds`);

db.close();
console.log('✅ Database cleanup completed successfully!');