# 🔗 Local Webhook Setup Guide

## 🎯 **Problem: Stripe Needs Public URL**

Stripe webhooks need a **publicly accessible URL**, but your local server (`http://localhost:4242`) isn't accessible from the internet.

## 🚀 **Solution: Use ngrok (Recommended)**

### **Step 1: Install ngrok**

**Option A: Download from website**
1. Go to [ngrok.com](https://ngrok.com)
2. Sign up for free account
3. Download ngrok for your OS
4. Extract and add to PATH

**Option B: Install via npm**
```bash
npm install -g ngrok
```

### **Step 2: Start Your Backend Server**
```bash
cd backend
npm start
```
Keep this running (you should see "Server running on port 4242")

### **Step 3: Expose Local Server with ngrok**

**In a new terminal:**
```bash
ngrok http 4242
```

**You'll see output like:**
```
ngrok by @inconshreveable

Session Status                online
Account                       <EMAIL>
Version                       3.0.0
Region                        United States (us)
Forwarding                    https://abc123.ngrok.io -> http://localhost:4242
Forwarding                    http://abc123.ngrok.io -> http://localhost:4242

Connections                   ttl     opn     rt1     rt5     p50     p90
                              0       0       0.00    0.00    0.00    0.00
```

**Copy the HTTPS URL** (e.g., `https://abc123.ngrok.io`)

### **Step 4: Set Up Stripe Webhooks**

**Go back to Stripe Dashboard:**

#### **Webhook 1 - Payments**
- **Endpoint URL:** `https://abc123.ngrok.io/api/payments/webhook`
- **Description:** `Local testing - payments`
- **Events:** `checkout.session.completed`
- **Click "Create destination"**
- **Copy the webhook secret** (starts with `whsec_`)

#### **Webhook 2 - Connect**
- **Click "Add endpoint" again**
- **Endpoint URL:** `https://abc123.ngrok.io/api/stripe-connect/webhook`
- **Description:** `Local testing - connect`
- **Events:** Select these:
  - `account.updated`
  - `transfer.created`
  - `transfer.paid`
  - `transfer.failed`
- **Click "Create destination"**
- **Copy the webhook secret** (starts with `whsec_`)

### **Step 5: Update Your .env File**

Update `backend/.env` with the webhook secrets:

```bash
# Replace with your actual webhook secrets from Stripe
STRIPE_WEBHOOK_SECRET=whsec_your_payment_webhook_secret_here
STRIPE_CONNECT_WEBHOOK_SECRET=whsec_your_connect_webhook_secret_here
```

### **Step 6: Restart Backend Server**

```bash
# Stop the server (Ctrl+C)
# Start it again
npm start
```

---

## 🧪 **Test the Webhooks**

### **Test Payment Webhook:**
1. **Add funds** to your account using test card
2. **Check backend logs** - should see:
   ```
   🔄 Webhook received: checkout.session.completed
   ✅ Balance updated: +1000 cents for user #$testuser
   ```

### **Test Connect Webhook:**
1. **Complete Stripe verification** for a user
2. **Check backend logs** - should see:
   ```
   🔄 Stripe Connect Webhook received: account.updated
   ✅ Updated account status for testuser: onboarded=true
   ```

---

## 🔧 **Alternative: Stripe CLI (Advanced)**

If ngrok doesn't work, you can use Stripe CLI:

```bash
# Install Stripe CLI
npm install -g stripe-cli

# Login to Stripe
stripe login

# Forward webhooks to local server
stripe listen --forward-to localhost:4242/api/payments/webhook

# In another terminal, forward Connect webhooks
stripe listen --forward-to localhost:4242/api/stripe-connect/webhook --events account.updated,transfer.created,transfer.paid,transfer.failed
```

---

## ⚠️ **Important Notes**

### **ngrok URL Changes**
- **Free ngrok URLs change** every time you restart ngrok
- **Update Stripe webhooks** with new URL each time
- **Consider ngrok paid plan** for static URLs if testing frequently

### **Keep Everything Running**
You need **3 terminals** running:
1. **Backend server:** `npm start`
2. **ngrok:** `ngrok http 4242`
3. **Flutter app:** `flutter run -d chrome`

### **Security**
- **ngrok URLs are temporary** and safe for testing
- **Don't share ngrok URLs** publicly
- **Use test Stripe keys only**

---

## 🎯 **Quick Setup Summary**

```bash
# Terminal 1: Start backend
cd backend && npm start

# Terminal 2: Expose with ngrok
ngrok http 4242

# Terminal 3: Start Flutter
cd flutter_app && flutter run -d chrome
```

**Then:**
1. Copy ngrok HTTPS URL
2. Add to Stripe webhooks
3. Copy webhook secrets to .env
4. Restart backend
5. Test! 🚀

---

## 🐛 **Troubleshooting**

### **ngrok not found**
```bash
# Install globally
npm install -g ngrok

# Or download from ngrok.com
```

### **Webhook not receiving events**
1. **Check ngrok is running** and shows the correct URL
2. **Verify webhook URL** in Stripe matches ngrok URL exactly
3. **Check backend logs** for incoming requests
4. **Restart backend** after updating .env

### **"Webhook signature failed"**
1. **Copy webhook secret** exactly from Stripe Dashboard
2. **Update .env file** with correct secret
3. **Restart backend server**

**Need help with any step?** Let me know! 🎯