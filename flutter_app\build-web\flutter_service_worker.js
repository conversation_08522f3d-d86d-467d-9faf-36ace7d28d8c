'use strict';
const MANIFEST = 'flutter-app-manifest';
const TEMP = 'flutter-temp-cache';
const CACHE_NAME = 'flutter-app-cache';

const RESOURCES = {"assets/AssetManifest.bin": "cb4e6171459601130b736a11f22149d3",
"assets/AssetManifest.bin.json": "c443a26bb6dc1e6c3ec10f5edd7b22c6",
"assets/AssetManifest.json": "8a2b2d5cc197043fe76c15393eb39e9e",
"assets/assets/icons/bluesky.svg": "9c95fef8940551ffe2a99b0a7237f462",
"assets/assets/icons/facebook.svg": "e4e9a2173656a39df687e7c0dd6335db",
"assets/assets/icons/fanbase.svg": "dad0452cd08308fbf3ee7a643380112f",
"assets/assets/icons/gmail.svg": "febead9156934b31f071a1e4664d9122",
"assets/assets/icons/googlemessages.svg": "6d0a5928ff069c5f4fb1256690d2c74e",
"assets/assets/icons/imessage.svg": "f1e14b7c0ac55c3c2d2a906a02f3b380",
"assets/assets/icons/instagram.svg": "8e8d554c282bd215a21fa0bcf813f097",
"assets/assets/icons/medium.svg": "b0088ceb90f2f116c4366435a3f0c4f8",
"assets/assets/icons/messenger.svg": "29000729679b98f76a03b7255f43afab",
"assets/assets/icons/reddit.svg": "cea316dddbe6beb97b35660cc9718435",
"assets/assets/icons/share.svg": "be05035a5611d76169f8c175b07acdd6",
"assets/assets/icons/substack.svg": "1da0c4feae8d94553b0bc94487f8f5e8",
"assets/assets/icons/threads.svg": "910f7e619e21f94f255d25565f607f23",
"assets/assets/icons/tiktok.svg": "068c4facb7efd7696d001f47ace6aae5",
"assets/assets/icons/truthsocial.svg": "c3c500a9568c8ac107864c605c35217a",
"assets/assets/icons/x.svg": "591d75f49453af0b3701f0503b445b6a",
"assets/assets/logo.png": "5a96d34bce92b475496a0c42513d790a",
"assets/FontManifest.json": "dc3d03800ccca4601324923c0b1d6d57",
"assets/fonts/MaterialIcons-Regular.otf": "ce56b34bf77436cc2aa4d890651ee9dc",
"assets/NOTICES": "ee1e2996b5b111896360081bc4feacca",
"assets/packages/country_state_city/lib/assets/city.json": "5a9057cfd2f210de21a8aae8d93ebee0",
"assets/packages/country_state_city/lib/assets/country.json": "b68fc9d8b1a459e788a999e28dfabb59",
"assets/packages/country_state_city/lib/assets/state.json": "74a6cb64cfba2c64ea1cc3c8fd45dd6c",
"assets/packages/country_state_city_picker/lib/assets/country.json": "11b8187fd184a2d648d6b5be8c5e9908",
"assets/packages/cupertino_icons/assets/CupertinoIcons.ttf": "33b7d9392238c04c131b6ce224e13711",
"assets/packages/fluttertoast/assets/toastify.css": "a85675050054f179444bc5ad70ffc635",
"assets/packages/fluttertoast/assets/toastify.js": "56e2c9cedd97f10e7e5f1cebd85d53e3",
"assets/shaders/ink_sparkle.frag": "ecc85a2e95f5e9f53123dcaf8cb9b6ce",
"canvaskit/canvaskit.js": "140ccb7d34d0a55065fbd422b843add6",
"canvaskit/canvaskit.js.symbols": "58832fbed59e00d2190aa295c4d70360",
"canvaskit/canvaskit.wasm": "07b9f5853202304d3b0749d9306573cc",
"canvaskit/chromium/canvaskit.js": "5e27aae346eee469027c80af0751d53d",
"canvaskit/chromium/canvaskit.js.symbols": "193deaca1a1424049326d4a91ad1d88d",
"canvaskit/chromium/canvaskit.wasm": "24c77e750a7fa6d474198905249ff506",
"canvaskit/skwasm.js": "1ef3ea3a0fec4569e5d531da25f34095",
"canvaskit/skwasm.js.symbols": "0088242d10d7e7d6d2649d1fe1bda7c1",
"canvaskit/skwasm.wasm": "264db41426307cfc7fa44b95a7772109",
"canvaskit/skwasm_heavy.js": "413f5b2b2d9345f37de148e2544f584f",
"canvaskit/skwasm_heavy.js.symbols": "3c01ec03b5de6d62c34e17014d1decd3",
"canvaskit/skwasm_heavy.wasm": "8034ad26ba2485dab2fd49bdd786837b",
"favicon.png": "14656d7735277fba58d45b3af6bb1ce4",
"flutter.js": "888483df48293866f9f41d3d9274a779",
"flutter_bootstrap.js": "e6b222f173351ef6749d6136146027fd",
"icons/Icon-192.png": "e61ceadffad08c210eda20a7f452a104",
"icons/Icon-512.png": "e2e0753929c4e9836fa152b73a0f45de",
"icons/Icon-maskable-192.png": "e61ceadffad08c210eda20a7f452a104",
"icons/Icon-maskable-512.png": "e2e0753929c4e9836fa152b73a0f45de",
"index.html": "b8a454b82e43580a84d887c083acbc44",
"/": "b8a454b82e43580a84d887c083acbc44",
"main.dart.js": "fc3202affc9a31203d4bae9122a772c0",
"manifest.json": "9fb4fa46ed0739bd62e21f45cf86871e",
"splash/img/dark-1x.png": "232f402cd1f1eec4247697704e7472e9",
"splash/img/dark-2x.png": "be5e37fec5cf5fbe4670f15695c93c71",
"splash/img/dark-3x.png": "e4281e5eb4f18562292b7e8a406fb5b0",
"splash/img/dark-4x.png": "2d44f04d8a8ab27c4132892e26815d25",
"splash/img/light-1x.png": "232f402cd1f1eec4247697704e7472e9",
"splash/img/light-2x.png": "be5e37fec5cf5fbe4670f15695c93c71",
"splash/img/light-3x.png": "e4281e5eb4f18562292b7e8a406fb5b0",
"splash/img/light-4x.png": "2d44f04d8a8ab27c4132892e26815d25",
"version.json": "16387c448038f60c0cbf71b6dcd1053b"};
// The application shell files that are downloaded before a service worker can
// start.
const CORE = ["main.dart.js",
"index.html",
"flutter_bootstrap.js",
"assets/AssetManifest.bin.json",
"assets/FontManifest.json"];

// During install, the TEMP cache is populated with the application shell files.
self.addEventListener("install", (event) => {
  self.skipWaiting();
  return event.waitUntil(
    caches.open(TEMP).then((cache) => {
      return cache.addAll(
        CORE.map((value) => new Request(value, {'cache': 'reload'})));
    })
  );
});
// During activate, the cache is populated with the temp files downloaded in
// install. If this service worker is upgrading from one with a saved
// MANIFEST, then use this to retain unchanged resource files.
self.addEventListener("activate", function(event) {
  return event.waitUntil(async function() {
    try {
      var contentCache = await caches.open(CACHE_NAME);
      var tempCache = await caches.open(TEMP);
      var manifestCache = await caches.open(MANIFEST);
      var manifest = await manifestCache.match('manifest');
      // When there is no prior manifest, clear the entire cache.
      if (!manifest) {
        await caches.delete(CACHE_NAME);
        contentCache = await caches.open(CACHE_NAME);
        for (var request of await tempCache.keys()) {
          var response = await tempCache.match(request);
          await contentCache.put(request, response);
        }
        await caches.delete(TEMP);
        // Save the manifest to make future upgrades efficient.
        await manifestCache.put('manifest', new Response(JSON.stringify(RESOURCES)));
        // Claim client to enable caching on first launch
        self.clients.claim();
        return;
      }
      var oldManifest = await manifest.json();
      var origin = self.location.origin;
      for (var request of await contentCache.keys()) {
        var key = request.url.substring(origin.length + 1);
        if (key == "") {
          key = "/";
        }
        // If a resource from the old manifest is not in the new cache, or if
        // the MD5 sum has changed, delete it. Otherwise the resource is left
        // in the cache and can be reused by the new service worker.
        if (!RESOURCES[key] || RESOURCES[key] != oldManifest[key]) {
          await contentCache.delete(request);
        }
      }
      // Populate the cache with the app shell TEMP files, potentially overwriting
      // cache files preserved above.
      for (var request of await tempCache.keys()) {
        var response = await tempCache.match(request);
        await contentCache.put(request, response);
      }
      await caches.delete(TEMP);
      // Save the manifest to make future upgrades efficient.
      await manifestCache.put('manifest', new Response(JSON.stringify(RESOURCES)));
      // Claim client to enable caching on first launch
      self.clients.claim();
      return;
    } catch (err) {
      // On an unhandled exception the state of the cache cannot be guaranteed.
      console.error('Failed to upgrade service worker: ' + err);
      await caches.delete(CACHE_NAME);
      await caches.delete(TEMP);
      await caches.delete(MANIFEST);
    }
  }());
});
// The fetch handler redirects requests for RESOURCE files to the service
// worker cache.
self.addEventListener("fetch", (event) => {
  if (event.request.method !== 'GET') {
    return;
  }
  var origin = self.location.origin;
  var key = event.request.url.substring(origin.length + 1);
  // Redirect URLs to the index.html
  if (key.indexOf('?v=') != -1) {
    key = key.split('?v=')[0];
  }
  if (event.request.url == origin || event.request.url.startsWith(origin + '/#') || key == '') {
    key = '/';
  }
  // If the URL is not the RESOURCE list then return to signal that the
  // browser should take over.
  if (!RESOURCES[key]) {
    return;
  }
  // If the URL is the index.html, perform an online-first request.
  if (key == '/') {
    return onlineFirst(event);
  }
  event.respondWith(caches.open(CACHE_NAME)
    .then((cache) =>  {
      return cache.match(event.request).then((response) => {
        // Either respond with the cached resource, or perform a fetch and
        // lazily populate the cache only if the resource was successfully fetched.
        return response || fetch(event.request).then((response) => {
          if (response && Boolean(response.ok)) {
            cache.put(event.request, response.clone());
          }
          return response;
        });
      })
    })
  );
});
self.addEventListener('message', (event) => {
  // SkipWaiting can be used to immediately activate a waiting service worker.
  // This will also require a page refresh triggered by the main worker.
  if (event.data === 'skipWaiting') {
    self.skipWaiting();
    return;
  }
  if (event.data === 'downloadOffline') {
    downloadOffline();
    return;
  }
});
// Download offline will check the RESOURCES for all files not in the cache
// and populate them.
async function downloadOffline() {
  var resources = [];
  var contentCache = await caches.open(CACHE_NAME);
  var currentContent = {};
  for (var request of await contentCache.keys()) {
    var key = request.url.substring(origin.length + 1);
    if (key == "") {
      key = "/";
    }
    currentContent[key] = true;
  }
  for (var resourceKey of Object.keys(RESOURCES)) {
    if (!currentContent[resourceKey]) {
      resources.push(resourceKey);
    }
  }
  return contentCache.addAll(resources);
}
// Attempt to download the resource online before falling back to
// the offline cache.
function onlineFirst(event) {
  return event.respondWith(
    fetch(event.request).then((response) => {
      return caches.open(CACHE_NAME).then((cache) => {
        cache.put(event.request, response.clone());
        return response;
      });
    }).catch((error) => {
      return caches.open(CACHE_NAME).then((cache) => {
        return cache.match(event.request).then((response) => {
          if (response != null) {
            return response;
          }
          throw error;
        });
      });
    })
  );
}
