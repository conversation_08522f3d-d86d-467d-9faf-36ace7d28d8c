import 'dart:convert';
import 'package:universal_html/html.dart' as html;
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;
import '../constants/theme_constants.dart';
import '../services/api.dart';
import '../services/balance_provider.dart';
import '../widgets/enhanced_share_widget.dart';
import 'give_cashOut_screen.dart';
import 'cashout_screen.dart';
import 'topup_screen.dart';

class UserProfileView extends StatefulWidget {
  final String username; // Current user's username

  const UserProfileView({
    super.key,
    required this.username,
  });

  @override
  State<UserProfileView> createState() => _UserProfileViewState();
}

class _UserProfileViewState extends State<UserProfileView> {
  Map<String, dynamic> currentUser = {}; // Current user profile data
  bool loading = true;
  Uint8List? profileImageBytes; // Profile image

  // Right column state management
  bool showUserSearch = true; // true = search mode, false = give/cashout mode
  List users = [];
  List filteredUsers = [];
  String searchQuery = "";
  Map<String, dynamic>? selectedUser; // Selected user for give/cashout
  Uint8List? selectedUserImageBytes;
  double selectedUserBalance = 0.0;
  double customAmount = 0.25;

  @override
  void initState() {
    super.initState();
    _loadProfile();
    _loadUsers();
  }

  Future<void> _loadProfile() async {
    if (mounted) setState(() => loading = true);
    try {
      // Load current user's profile
      final userData = await Api.getUserByUsername(widget.username);

      if (userData == null) {
        throw Exception('Failed to load user data');
      }

      // Set the user data first
      if (mounted) {
        setState(() {
          currentUser = userData;
        });
      }

      // Refresh balance from provider (but don't await it blocking the UI)
      final balanceProvider =
          Provider.of<BalanceProvider>(context, listen: false);
      balanceProvider.refreshBalance(); // Remove await here - let it run async

      // Handle profile picture loading separately and safely
      await _loadProfilePicture(userData);
    } catch (e) {
      debugPrint('Error loading profile: $e');
      Fluttertoast.showToast(msg: 'Error loading profile: $e');
    } finally {
      if (mounted) setState(() => loading = false);
    }
  }

  Future<void> _loadProfilePicture(Map<String, dynamic> userData) async {
    final profilePicUrl = userData['profile_picture']?.toString();
    if (profilePicUrl == null || profilePicUrl.isEmpty) {
      // Clear any existing profile image
      if (mounted) {
        setState(() => profileImageBytes = null);
      }
      return;
    }

    try {
      Uint8List? imageBytes;

      if (profilePicUrl.startsWith('data:image')) {
        final base64Data = profilePicUrl.split(',')[1];
        imageBytes = base64Decode(base64Data);
      } else if (!profilePicUrl.startsWith('http')) {
        final fullUrl = '${Api.base}${profilePicUrl}';
        final pictureRes = await http.get(Uri.parse(fullUrl));
        if (pictureRes.statusCode == 200) {
          imageBytes = pictureRes.bodyBytes;
        }
      }

      if (mounted && imageBytes != null) {
        setState(() => profileImageBytes = imageBytes);
      }
    } catch (e) {
      debugPrint('Error loading profile picture: $e');
      // Don't show toast for image loading errors, just log them
    }
  }

  void _copyProfileHandle() {
    // Strip #$ prefix for cleaner sharing URLs
    final cleanUsername = widget.username.startsWith('#\$')
        ? widget.username.substring(2) // Remove #$ prefix
        : widget.username;
    final profileUrl =
        "https://www.hashtagdollars.com/social-profile?username=${Uri.encodeComponent(cleanUsername)}";

    html.window.navigator.clipboard?.writeText(profileUrl);
    Fluttertoast.showToast(msg: "Profile link copied!");
  }

  void _navigateToEditProfile() {
    Navigator.pushNamed(context, '/userprofile', arguments: widget.username);
  }

  void _navigateToGiveCashout() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => GiveCashOutScreen(
          fromUsername: widget.username,
          toUsername: widget.username,
        ),
      ),
    );
  }

  Future<void> _loadUsers() async {
    try {
      final u = await Api.fetchUsers();
      if (mounted) {
        setState(() {
          users = u ?? [];
          _filterUsers();
        });
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'Unable to fetch users: $e');
    }
  }

  void _filterUsers() {
    filteredUsers = users
        .where((u) =>
            u['username'] != widget.username &&
            ((u['username'] ?? '')
                    .toString()
                    .toLowerCase()
                    .contains(searchQuery.toLowerCase()) ||
                (u['display_name'] ?? '')
                    .toString()
                    .toLowerCase()
                    .contains(searchQuery.toLowerCase())))
        .toList();
    if (mounted) setState(() {});
  }

  void _selectUser(Map<String, dynamic> user) async {
    try {
      // Load selected user's balance
      final bal = await Api.getBalanceByUsername(user['username']);
      if (mounted) {
        setState(() {
          selectedUser = user;
          selectedUserBalance = (bal ?? 0) / 100.0;
          showUserSearch = false;
        });
      }

      // Handle profile picture for selected user
      final profilePictureUrl = user['profile_picture']?.toString();
      if (profilePictureUrl != null && profilePictureUrl.isNotEmpty) {
        if (profilePictureUrl.startsWith('data:image')) {
          try {
            final base64Data = profilePictureUrl.split(',')[1];
            final bytes = base64Decode(base64Data);
            if (mounted) setState(() => selectedUserImageBytes = bytes);
          } catch (e) {
            debugPrint('Error decoding selected user profile picture: $e');
          }
        } else if (!profilePictureUrl.startsWith('http')) {
          final fullUrl = '${Api.base}${profilePictureUrl}';
          try {
            final pictureRes = await http.get(Uri.parse(fullUrl));
            if (pictureRes.statusCode == 200) {
              if (mounted)
                setState(() => selectedUserImageBytes = pictureRes.bodyBytes);
            }
          } catch (e) {
            debugPrint('Error loading selected user profile picture: $e');
          }
        }
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error loading user data: $e');
    }
  }

  void _backToSearch() {
    if (mounted) {
      setState(() {
        showUserSearch = true;
        selectedUser = null;
        selectedUserImageBytes = null;
        selectedUserBalance = 0.0;
        customAmount = 0.25;
      });
    }
  }

  Future<void> _donate(double amount) async {
    if (amount <= 0 || selectedUser == null) return;

    final balanceProvider =
        Provider.of<BalanceProvider>(context, listen: false);
    if (amount > balanceProvider.balance) {
      Fluttertoast.showToast(
          msg: "Insufficient balance. Please add funds first.");
      return;
    }

    // Show loading state
    bool wasLoading = loading;
    if (mounted) setState(() => loading = true);

    try {
      final amountCents = (amount * 100).toInt();
      final result = await Api.donateFromBalance(
        fromUsername: widget.username,
        toUsername: selectedUser!['username'],
        amountCents: amountCents,
        feeCents: 0,
      );

      if (result?['error'] != null) {
        Fluttertoast.showToast(msg: "Donation Error: ${result!['error']}");
        return;
      }

      Fluttertoast.showToast(
          msg:
              "✅ Donation of \$${amount.toStringAsFixed(2)} sent successfully!");

      // Update balance directly from API response
      final newBalances = result?['newBalances'] as Map<String, dynamic>?;
      final donorBalance = newBalances?['donor'] ?? 0;
      balanceProvider.updateBalance(donorBalance / 100.0);

      if (mounted) {
        setState(() {
          selectedUserBalance =
              (result?['newBalances']?['recipient'] ?? 0) / 100.0;
        });
      }

      // Reload data - but do it sequentially and with proper error handling
      await Future.wait([
        _loadProfile(),
      ]);
    } catch (e) {
      debugPrint('Donation error: $e');
      Fluttertoast.showToast(msg: "Error: $e");
    } finally {
      // Restore previous loading state
      if (mounted) setState(() => loading = wasLoading);
    }
  }

  void _navigateToCashOut() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => CashOutScreen(username: widget.username),
      ),
    );
  }

  void _goToTopUp() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => TopupScreen(username: widget.username),
      ),
    );
  }

  void _logout() async {
    await Api.logout();
    if (mounted) {
      Navigator.pushReplacementNamed(context, '/landing');
    }
  }

  void _showShareBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.cardBackground,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => EnhancedShareWidget(
        contentId: widget.username,
        contentText: currentUser['bio'] ??
            currentUser['display_name'] ??
            'Check out this profile',
        contentType: 'profile',
      ),
    );
  }

  Widget _buildProfileSection() {
    final isMobile = MediaQuery.of(context).size.width <= 768;
    final profileDisplay = currentUser['display_name'] ?? '';
    final profileEmail = currentUser['email'] ?? '';
    final profileBio = currentUser['bio'] ?? '';
    final profilePhone = currentUser['phone'] ?? '';
    final profileUsername = currentUser['username'] ?? '';
    final profileCountry = currentUser['country'] ?? '';
    final profileState = currentUser['state'] ?? '';
    final profileCity = currentUser['city'] ?? '';
    final profileUrl1 = currentUser['url1'] ?? '';
    final profileUrl2 = currentUser['url2'] ?? '';
    final profileUrl3 = currentUser['url3'] ?? '';
    final profileUrl4 = currentUser['url4'] ?? '';
    final profileUrl5 = currentUser['url5'] ?? '';
    final profileHandle = widget.username.startsWith('#\$')
        ? widget.username
        : "#\${widget.username}";

    return Container(
      height: MediaQuery.of(context).size.height *
          0.7, // Add back height constraint
      padding: EdgeInsets.all(isMobile ? 4 : 16),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Profile Picture
          CircleAvatar(
            radius: isMobile ? 30 : 50,
            backgroundColor: Colors.white,
            backgroundImage: profileImageBytes != null
                ? MemoryImage(profileImageBytes!)
                : null,
            child: profileImageBytes == null
                ? Icon(Icons.account_circle,
                    color: Colors.grey, size: isMobile ? 30 : 50)
                : null,
          ),
          SizedBox(height: isMobile ? 12 : 16),

          // Handle with copy button - FIXED LAYOUT
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                // Changed from Expanded wrapped in Center
                child: SelectableText(
                  profileHandle,
                  style: TextStyle(
                    fontSize: isMobile ? 12 : 16,
                    color: AppColors.buttonText,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              IconButton(
                icon: Icon(Icons.copy,
                    color: const Color.fromARGB(255, 0, 82, 169),
                    size: isMobile ? 16 : 20),
                onPressed: () => _copyProfileHandle(),
                tooltip: 'Copy handle',
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ],
          ),
          SizedBox(height: isMobile ? 8 : 16),

          // Scrollable content area for the rest
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Display Name - FIXED LAYOUT
                  if (profileDisplay.isNotEmpty) ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.person,
                            color: Colors.blueGrey, size: isMobile ? 18 : 20),
                        const SizedBox(width: 8),
                        Flexible(
                          // Changed from Expanded wrapped in Center
                          child: Text(
                            profileDisplay,
                            style: TextStyle(
                              fontSize: isMobile ? 12 : 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.black87,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: isMobile ? 8 : 12),
                  ],

                  // Email - FIXED LAYOUT
                  if (profileEmail.isNotEmpty) ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.email,
                            color: Colors.orange, size: isMobile ? 18 : 20),
                        const SizedBox(width: 8),
                        Flexible(
                          // Changed from Expanded wrapped in Center
                          child: SelectableText(
                            profileEmail,
                            style: TextStyle(
                                fontSize: isMobile ? 10 : 16,
                                color: Colors.black87),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: isMobile ? 8 : 12),
                  ],

                  // Bio - FIXED LAYOUT
                  if (profileBio.isNotEmpty) ...[
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.info,
                            color: Colors.purple, size: isMobile ? 18 : 20),
                        const SizedBox(width: 8),
                        Flexible(
                          // Changed from Expanded wrapped in Center
                          child: Text(
                            profileBio,
                            style: TextStyle(
                                fontSize: isMobile ? 12 : 16,
                                color: Colors.black87),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: isMobile ? 8 : 12),
                  ],

                  // Phone
                  if (profilePhone.isNotEmpty) ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.phone,
                            color: Colors.teal, size: isMobile ? 18 : 20),
                        const SizedBox(width: 8),
                        SelectableText(
                          profilePhone,
                          style: TextStyle(
                              fontSize: isMobile ? 12 : 16,
                              color: Colors.black87),
                        ),
                      ],
                    ),
                    SizedBox(height: isMobile ? 8 : 12),
                  ],

                  // City, State, Country
                  if (profileCity.isNotEmpty ||
                      profileState.isNotEmpty ||
                      profileCountry.isNotEmpty) ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.location_on,
                            color: Colors.red, size: isMobile ? 18 : 20),
                        const SizedBox(width: 8),
                        Flexible(
                          // Changed from Center
                          child: isMobile
                              ? Column(
                                  children: [
                                    if (profileCity.isNotEmpty)
                                      Text(
                                        profileCity,
                                        style: TextStyle(
                                            fontSize: isMobile ? 12 : 16,
                                            color: Colors.black87),
                                      ),
                                    if (profileState.isNotEmpty)
                                      Text(
                                        profileState,
                                        style: TextStyle(
                                            fontSize: isMobile ? 12 : 16,
                                            color: Colors.black87),
                                      ),
                                    if (profileCountry.isNotEmpty)
                                      Text(
                                        profileCountry,
                                        style: TextStyle(
                                            fontSize: isMobile ? 12 : 16,
                                            color: Colors.black87),
                                      ),
                                  ],
                                )
                              : Text(
                                  [profileCity, profileState, profileCountry]
                                      .where((s) => s.isNotEmpty)
                                      .join(', '),
                                  style: TextStyle(
                                      fontSize: isMobile ? 12 : 16,
                                      color: Colors.black87),
                                  textAlign: TextAlign.center,
                                ),
                        ),
                      ],
                    ),
                    SizedBox(height: isMobile ? 6 : 12),
                  ],

                  // URLs
                  if (profileUrl1.isNotEmpty) ...[
                    _buildClickableUrl(profileUrl1, profileUrl1, isMobile),
                    SizedBox(height: isMobile ? 6 : 8),
                  ],
                  if (profileUrl2.isNotEmpty) ...[
                    _buildClickableUrl(profileUrl2, profileUrl2, isMobile),
                    SizedBox(height: isMobile ? 6 : 8),
                  ],
                  if (profileUrl3.isNotEmpty) ...[
                    _buildClickableUrl(profileUrl3, profileUrl3, isMobile),
                    SizedBox(height: isMobile ? 6 : 8),
                  ],
                  if (profileUrl4.isNotEmpty) ...[
                    _buildClickableUrl(profileUrl4, profileUrl4, isMobile),
                    SizedBox(height: isMobile ? 6 : 8),
                  ],
                  if (profileUrl5.isNotEmpty) ...[
                    _buildClickableUrl(profileUrl5, profileUrl5, isMobile),
                  ],
                ],
              ),
            ),
          ),

          // Edit Profile Button - Fixed at bottom
          Padding(
            padding: EdgeInsets.only(top: isMobile ? 8 : 16),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                style: AppButtonStyles.primaryButton,
                onPressed: _navigateToEditProfile,
                icon: const Icon(Icons.edit),
                label: Text('Edit Profile',
                    style: TextStyle(fontSize: isMobile ? 12 : 14)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRightColumn() {
    if (showUserSearch) {
      return _buildUserSearchSection();
    } else {
      return _buildGiveCashoutSection();
    }
  }

  Widget _buildUserSearchSection() {
    final isMobile = MediaQuery.of(context).size.width <= 768;
    return Container(
      height: MediaQuery.of(context).size.height * 0.7, // Add explicit height
      padding: EdgeInsets.all(isMobile ? 4 : 16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            'Find Users',
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: isMobile ? 12 : 18,
            ),
          ),
          const SizedBox(height: 16),

          // Search Field
          TextField(
            style: AppTextStyles.inputText,
            decoration: AppInputDecorations.primaryInput(
              hintText: "Search users...",
              suffixIcon: const Icon(Icons.search, color: Colors.white70),
            ),
            onChanged: (val) {
              searchQuery = val;
              _filterUsers();
            },
          ),
          const SizedBox(height: 16),

          // User List - With proper height constraint
          Expanded(
            // This will take remaining space
            child: filteredUsers.isEmpty
                ? const Center(child: Text('No users found'))
                : ListView.builder(
                    itemCount: filteredUsers.length,
                    itemBuilder: (ctx, i) {
                      final u = filteredUsers[i];

                      // Handle profile picture
                      Widget profileAvatar;
                      final profilePicture = u['profile_picture']?.toString();
                      if (profilePicture != null && profilePicture.isNotEmpty) {
                        if (profilePicture.startsWith('data:image')) {
                          try {
                            final base64Data = profilePicture.split(',')[1];
                            final bytes = base64Decode(base64Data);
                            profileAvatar = CircleAvatar(
                              backgroundImage: MemoryImage(bytes),
                              radius: isMobile ? 12 : 20,
                            );
                          } catch (e) {
                            profileAvatar = CircleAvatar(
                              child: const Icon(Icons.person),
                              radius: isMobile ? 12 : 20,
                            );
                          }
                        } else if (profilePicture.startsWith('http')) {
                          profileAvatar = CircleAvatar(
                            backgroundImage: NetworkImage(profilePicture),
                            radius: isMobile ? 12 : 20,
                          );
                        } else {
                          profileAvatar = CircleAvatar(
                            child: const Icon(Icons.person),
                            radius: isMobile ? 12 : 20,
                          );
                        }
                      } else {
                        profileAvatar = CircleAvatar(
                          child: const Icon(Icons.person),
                          radius: isMobile ? 12 : 20,
                        );
                      }

                      return Card(
                        margin: EdgeInsets.symmetric(
                            vertical: isMobile ? 1 : 4, horizontal: 0),
                        child: ListTile(
                          subtitleTextStyle: TextStyle(
                            fontSize: isMobile ? 10 : 12,
                          ),
                          leading: profileAvatar,
                          title: Text(
                            u['username'] ?? '',
                            style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: isMobile ? 12 : 16),
                          ),
                          subtitle: Text(
                            u['display_name'] ?? '',
                            style: TextStyle(
                              fontSize: isMobile ? 10 : 12,
                              color: AppColors.buttonText.withAlpha(128),
                            ),
                          ),
                          onTap: () => _selectUser(u),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildGiveCashoutSection() {
    if (selectedUser == null) return const SizedBox();

    final isMobile = MediaQuery.of(context).size.width <= 768;

    return Container(
      padding: EdgeInsets.all(isMobile ? 4 : 16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Back to Search Button
          Align(
            alignment: Alignment.topLeft,
            child: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: _backToSearch,
              tooltip: 'Back to Search',
            ),
          ),

          SizedBox(height: isMobile ? 6 : 8),

          Text(
            'Give & Cashout',
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: isMobile ? 16 : 18,
            ),
          ),

          const SizedBox(height: 20),

          // Profile Pictures with Balances - Responsive
          Consumer<BalanceProvider>(
            builder: (context, balanceProvider, child) {
              final isMobile = MediaQuery.of(context).size.width <= 768;
              final avatarRadius = isMobile ? 25.0 : 30.0;

              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Current User (Giver)
                  Column(
                    children: [
                      Text(
                        '\$${balanceProvider.balance.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: isMobile ? 10 : 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      CircleAvatar(
                        radius: avatarRadius,
                        backgroundColor: Colors.white,
                        backgroundImage: profileImageBytes != null
                            ? MemoryImage(profileImageBytes!)
                            : null,
                        child: profileImageBytes == null
                            ? Icon(Icons.account_circle,
                                color: Colors.grey, size: avatarRadius)
                            : null,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'You',
                        style: TextStyle(
                            fontSize: isMobile ? 10 : 12,
                            fontWeight: FontWeight.w500),
                      ),
                    ],
                  ),

                  // Arrow
                  Icon(Icons.arrow_forward,
                      color: Colors.white70, size: isMobile ? 20 : 24),

                  // Selected User (Receiver)
                  Column(
                    children: [
                      Text(
                        '\$${selectedUserBalance.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: isMobile ? 10 : 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.limeAccent,
                        ),
                      ),
                      const SizedBox(height: 4),
                      CircleAvatar(
                        radius: avatarRadius,
                        backgroundColor: Colors.white,
                        backgroundImage: selectedUserImageBytes != null
                            ? MemoryImage(selectedUserImageBytes!)
                            : null,
                        child: selectedUserImageBytes == null
                            ? Icon(Icons.account_circle,
                                color: Colors.grey, size: avatarRadius)
                            : null,
                      ),
                      const SizedBox(height: 4),
                      SizedBox(
                        width: isMobile ? 60 : 80, // Constrain width on mobile
                        child: Text(
                          selectedUser!['display_name'] ??
                              selectedUser!['username'] ??
                              '',
                          style: TextStyle(
                              fontSize: isMobile ? 10 : 12,
                              fontWeight: FontWeight.w500),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              );
            },
          ),

          const SizedBox(height: 20),

          // Donation Amount Input
          TextField(
            style: AppTextStyles.inputText,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            decoration: AppInputDecorations.primaryInput(
              hintText: "Donation Amount (USD)",
            ),
            onChanged: (val) {
              setState(() {
                customAmount = double.tryParse(val) ?? 0.25;
              });
            },
          ),

          const SizedBox(height: 12),

          // Give Button
          Consumer<BalanceProvider>(
            builder: (context, balanceProvider, child) => Column(
              children: [
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    style: AppButtonStyles.primaryButton.copyWith(
                      backgroundColor: MaterialStateProperty.all(
                        (customAmount > 0 &&
                                customAmount <= balanceProvider.balance)
                            ? Colors.blue
                            : Colors.grey,
                      ),
                    ),
                    onPressed: (customAmount > 0 &&
                            customAmount <= balanceProvider.balance)
                        ? () => _donate(customAmount)
                        : null,
                    child: Text(
                      customAmount > balanceProvider.balance
                          ? "Insufficient Balance"
                          : "Give \$${customAmount.toStringAsFixed(2)}",
                    ),
                  ),
                ),
                if (customAmount > balanceProvider.balance)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      'Add funds to your account to make donations',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.warningText,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Cash Out Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              style: AppButtonStyles.primaryButton.copyWith(
                backgroundColor: MaterialStateProperty.all(Colors.green),
              ),
              onPressed: _navigateToCashOut,
              icon: const Icon(Icons.money),
              label: const Text('Cash Out'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClickableUrl(String label, String url, bool isMobile) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.link,
            color: const Color.fromARGB(255, 0, 82, 169),
            size: isMobile ? 18 : 20),
        const SizedBox(width: 8),
        Expanded(
          child: InkWell(
            onTap: () {
              try {
                html.window.open(
                    url.startsWith('http') ? url : 'https://$url', '_blank');
              } catch (e) {
                Fluttertoast.showToast(msg: 'Could not open link');
              }
            },
            child: Text(
              label,
              style: TextStyle(
                fontSize: isMobile ? 12 : 16,
                color: const Color.fromARGB(255, 0, 82, 169),
                decoration: TextDecoration.underline,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final shortHandle = widget.username.startsWith('#\$')
        ? widget.username
        : "#\${widget.username}";
    final isMobile = MediaQuery.of(context).size.width <= 768;

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.home),
          onPressed: () => Navigator.pop(context),
        ),
        title: Builder(
          builder: (context) {
            final screenWidth = MediaQuery.of(context).size.width;
            final isWideScreen = screenWidth > 600; // Tablet/desktop breakpoint

            return isWideScreen
                ? Row(
                    children: [
                      Image.asset('assets/logo.png', height: 40),
                      const SizedBox(width: 8),
                      Text('Profile - ${widget.username}'),
                    ],
                  )
                : Image.asset('assets/logo.png',
                    height: 40); // Just logo on mobile
          },
        ),
        actions: [
          // Centered actions using Expanded (same as Home screen)
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  icon: const Icon(Icons.share),
                  tooltip: 'Share Profile',
                  onPressed: _showShareBottomSheet,
                ),
                IconButton(
                  icon: const Icon(Icons.account_balance_wallet),
                  tooltip: 'Add Funds',
                  onPressed: _goToTopUp,
                ),
                IconButton(
                  icon: const Icon(Icons.money),
                  tooltip: 'Cash Out',
                  onPressed: _navigateToCashOut,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 2),
                  child: Tooltip(
                    message: 'Profile',
                    child: GestureDetector(
                      onTap: _navigateToEditProfile,
                      child: CircleAvatar(
                        radius: 16,
                        backgroundColor: Colors.white,
                        backgroundImage: profileImageBytes != null
                            ? MemoryImage(profileImageBytes!) as ImageProvider
                            : null,
                        child: profileImageBytes == null
                            ? const Icon(Icons.account_circle,
                                color: Colors.purple, size: 20)
                            : null,
                      ),
                    ),
                  ),
                ),
                // Refresh button
                IconButton(
                  icon: const Icon(Icons.refresh),
                  tooltip: 'Refresh',
                  onPressed: () {
                    _loadProfile();
                    _loadUsers();
                  },
                ),
              ],
            ),
          ),
          // Logout button on the right
          IconButton(
            icon: const Icon(Icons.logout),
            tooltip: 'Logout',
            onPressed: _logout,
          ),
        ],
      ),
      body: Container(
        color: AppColors.roseGold, // Rose Gold background
        child: Center(
          child: ConstrainedBox(
            constraints:
                const BoxConstraints(maxWidth: 1200), // Wider for two columns
            child: loading
                ? const Center(child: CircularProgressIndicator())
                : Padding(
                    padding: EdgeInsets.all(isMobile ? 4 : 16),
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          // Logo
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Image.asset('assets/logo.png', height: 50),
                              // Title
                              const Text(
                                'Hashtag Dollars',
                                style: AppTextStyles.title,
                              ),
                            ],
                          ),
                          const SizedBox(height: 20),

                          // Responsive Layout: Two columns on all screens with adjusted sizes for mobile
                          Builder(
                            builder: (context) {
                              final screenWidth =
                                  MediaQuery.of(context).size.width;
                              final isWideScreen = screenWidth > 768;

                              // Adjust flex ratios and margins based on screen size
                              final int profileFlex = isWideScreen ? 2 : 1;
                              final int rightFlex = isWideScreen ? 3 : 1;
                              final double margin = isWideScreen ? 16.0 : 4.0;

                              return Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // First Column: Profile Section
                                  Expanded(
                                    flex: profileFlex,
                                    child: Container(
                                      margin: EdgeInsets.only(right: margin),
                                      child: _buildProfileSection(),
                                    ),
                                  ),

                                  // Second Column: User Search / Give & Cashout
                                  Expanded(
                                    flex: rightFlex,
                                    child: Container(
                                      margin: EdgeInsets.only(left: margin),
                                      child: _buildRightColumn(),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),

                          const SizedBox(height: 20),
                        ],
                      ),
                    ),
                  ),
          ),
        ),
      ),
    );
  }
}
