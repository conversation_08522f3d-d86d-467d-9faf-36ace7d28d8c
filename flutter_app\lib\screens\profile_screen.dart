// lib/screens/profile_screen.dart
import 'dart:convert';
import 'dart:html' as html;
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:http/http.dart' as http;
import '../constants/theme_constants.dart';
import '../services/api.dart';
import 'cashout_screen.dart';

class ProfileScreen extends StatefulWidget {
  final String fromUsername; // logged-in user
  final String toUsername; // clicked user

  const ProfileScreen({
    super.key,
    required this.fromUsername,
    required this.toUsername,
  });

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  Map<String, dynamic> user = {};
  bool loading = true;
  double customAmount = 0.25;
  double balance = 0.0;
  Uint8List? profileImageBytes;

  // donation history + favorites
  List<Map<String, dynamic>> givers30Days = [];
  double totalReceived30Days = 0.0;
  List<Map<String, dynamic>> favoriteGivers = [];

  bool showShareIcons = false; // after donation

  @override
  void initState() {
    super.initState();
    _load();
    _loadDonationHistory();
    _loadFavorites();
  }

  Future<void> _load() async {
    setState(() => loading = true);
    try {
      final u = await Api.getUserByUsername(widget.toUsername);
      final bal = await Api.getBalanceByUsername(widget.fromUsername);
      setState(() {
        user = u ?? {};
        balance = (bal ?? 0) / 100.0;
      });
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error: $e');
    } finally {
      setState(() => loading = false);
    }
  }

  Future<void> _loadDonationHistory() async {
    try {
      final response = await http.get(Uri.parse(
          "${Api.baseUrl}/api/users/${widget.toUsername}/donations30days"));
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          givers30Days = List<Map<String, dynamic>>.from(data['givers'] ?? []);
          totalReceived30Days = ((data['total'] ?? 0) as int) / 100.0;
        });
      }
    } catch (_) {}
  }

  Future<void> _loadFavorites() async {
    try {
      final response = await http.get(Uri.parse(
          "${Api.baseUrl}/api/users/${widget.fromUsername}/favorites"));
      if (response.statusCode == 200) {
        setState(() {
          favoriteGivers =
              List<Map<String, dynamic>>.from(json.decode(response.body));
        });
      }
    } catch (_) {}
  }

  Future<void> _donate(double amount) async {
    if (amount <= 0) return;
    if (amount > balance) {
      Fluttertoast.showToast(msg: "Insufficient balance. Please add funds first.");
      return;
    }

    try {
      // Use new balance-based donation API
      final amountCents = (amount * 100).toInt();
      
      final result = await Api.donateFromBalance(
        fromUsername: widget.fromUsername,
        toUsername: widget.toUsername,
        amountCents: amountCents,
        feeCents: 0, // No fees on donations
      );

      if (result?['error'] != null) {
        final error = result!['error'] as String;
        if (error.contains('Insufficient balance')) {
          Fluttertoast.showToast(msg: "Insufficient balance. Please add funds first.");
        } else if (error.contains('not found')) {
          Fluttertoast.showToast(msg: "User not found. Please check the username.");
        } else {
          Fluttertoast.showToast(msg: "Donation Error: $error");
        }
        return;
      }

      // Success!
      final newBalances = result?['newBalances'] as Map<String, dynamic>?;
      final donorBalance = newBalances?['donor'] ?? 0;
      
      Fluttertoast.showToast(msg: "✅ Donation of \$${amount.toStringAsFixed(2)} sent successfully!");
      
      setState(() {
        balance = donorBalance / 100.0; // Update local balance
        showShareIcons = true;
      });
      
      _load(); // Refresh full data
      _loadDonationHistory();
    } catch (e) {
      Fluttertoast.showToast(msg: "Error: $e");
    }
  }

  Future<void> _navigateToCashOut() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CashOutScreen(username: widget.fromUsername),
      ),
    );
    
    if (result == true) {
      // Refresh balance after cash out
      _load();
    }
  }

  void _copyProfileHandle() {
    final handle = widget.toUsername.startsWith('#\$')
        ? widget.toUsername
        : "#\${widget.toUsername}";
    html.window.navigator.clipboard?.writeText(handle);
    Fluttertoast.showToast(msg: "Profile handle copied!");
  }

  void _addToFavorites(Map<String, dynamic> giver) async {
    try {
      final response = await http.post(
        Uri.parse("${Api.baseUrl}/api/users/${widget.fromUsername}/favorites"),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode({"giverUsername": giver['username']}),
      );
      if (response.statusCode == 200) {
        Fluttertoast.showToast(msg: "Added to favorites!");
        _loadFavorites();
      }
    } catch (_) {}
  }

  Widget _socialShareRow() {
    final username = widget.toUsername.startsWith('#\$')
        ? widget.toUsername
        : "#\${widget.toUsername}";
    final msg =
        "I just made a Micro Donation to $username and you should too 👉 https://www.hashtagdollars.com";
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        IconButton(
            icon: const Icon(Icons.facebook, color: Colors.blue),
            onPressed: () {
              html.window.open(
                  "https://www.facebook.com/sharer/sharer.php?u=${Uri.encodeComponent(msg)}",
                  "_blank");
            }),
        IconButton(
            icon: const Icon(Icons.share, color: Colors.lightBlue),
            onPressed: () {
              html.window.open(
                  "https://twitter.com/intent/tweet?text=${Uri.encodeComponent(msg)}",
                  "_blank");
            }),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final display = user['display_name'] ?? '';
    final shortHandle = widget.toUsername.startsWith('#\$')
        ? widget.toUsername
        : "#\${widget.toUsername}";

    return Scaffold(
      appBar: AppBar(title: Text(widget.toUsername)),
      body: Container(
        color: AppColors.roseGold, // Rose Gold background
        child: Column(
          children: [
            Expanded(
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 600),
                  child: loading
                      ? const Center(child: CircularProgressIndicator())
                      : Padding(
                          padding: const EdgeInsets.all(16),
                          child: SingleChildScrollView(
                            child: Column(
                              children: [
                                // Logo at top
                                Image.asset('assets/logo.png',
                                    height: 80), // Slightly larger logo
                                const SizedBox(height: 16),
                                
                                // Balance Display (for donor)
                                if (widget.fromUsername == widget.toUsername)
                                  Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.all(16),
                                    margin: const EdgeInsets.only(bottom: 16),
                                    decoration: BoxDecoration(
                                      color: Colors.green.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(color: Colors.green),
                                    ),
                                    child: Column(
                                      children: [
                                        Text(
                                          'Your Balance',
                                          style: AppTextStyles.bodyLarge,
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          '\$${balance.toStringAsFixed(2)}',
                                          style: AppTextStyles.title.copyWith(
                                            fontSize: 28,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.green,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[200],
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(display,
                                          style: const TextStyle(
                                              fontSize: 22,
                                              fontWeight: FontWeight.bold)),
                                      const SizedBox(height: 8),
                                      Text(
                                          'Profile Balance: \$${((user['balance'] ?? 0) / 100.0).toStringAsFixed(2)}'),
                                      const SizedBox(height: 16),
                                      Row(
                                        children: [
                                          SelectableText(shortHandle,
                                              style: const TextStyle(
                                                  color: Colors.blue)),
                                          IconButton(
                                            icon: const Icon(Icons.copy),
                                            onPressed: _copyProfileHandle,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 20),
                                
                                // Donation history 30 days
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("Givers in last 30 days",
                                          style: AppTextStyles.bodyLarge),
                                      const SizedBox(height: 8),
                                      for (var g in givers30Days)
                                        ListTile(
                                          leading: g['profile_picture'] != null
                                              ? CircleAvatar(
                                                  backgroundImage: NetworkImage(
                                                      g['profile_picture']))
                                              : const CircleAvatar(
                                                  child: Icon(Icons.person)),
                                          title: Text(g['username'],
                                              style: AppTextStyles.bodyMedium),
                                          trailing: IconButton(
                                            icon: const Icon(
                                                Icons.favorite_border,
                                                color: Colors.white),
                                            onPressed: () => _addToFavorites(g),
                                          ),
                                        ),
                                      const SizedBox(height: 8),
                                      Text(
                                          "Total received: \$${totalReceived30Days.toStringAsFixed(2)}",
                                          style: AppTextStyles.bodyMedium),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 20),
                                
                                // Actions
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                      color: Colors.white.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12)),
                                  child: Column(
                                    children: [
                                      // Show current balance for donor
                                      Container(
                                        width: double.infinity,
                                        padding: const EdgeInsets.all(12),
                                        margin: const EdgeInsets.only(bottom: 16),
                                        decoration: BoxDecoration(
                                          color: Colors.blue.withOpacity(0.1),
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                        child: Text(
                                          'Your Balance: \$${balance.toStringAsFixed(2)}',
                                          style: AppTextStyles.bodyLarge.copyWith(
                                            fontWeight: FontWeight.bold,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                      
                                      TextField(
                                        style: AppTextStyles.inputText,
                                        keyboardType: const TextInputType
                                            .numberWithOptions(decimal: true),
                                        decoration:
                                            AppInputDecorations.primaryInput(
                                          hintText: "Donation Amount (USD)",
                                        ),
                                        onChanged: (val) {
                                          setState(() {
                                            customAmount =
                                                double.tryParse(val) ?? 0.25;
                                          });
                                        },
                                      ),
                                      const SizedBox(height: 12),
                                      
                                      ElevatedButton(
                                        style: AppButtonStyles.primaryButton.copyWith(
                                          backgroundColor: MaterialStateProperty.all(
                                            (customAmount > 0 && customAmount <= balance)
                                                ? Colors.blue
                                                : Colors.grey,
                                          ),
                                        ),
                                        onPressed: (customAmount > 0 && customAmount <= balance)
                                            ? () => _donate(customAmount)
                                            : null,
                                        child: Text(
                                          customAmount > balance
                                              ? "Insufficient Balance"
                                              : "Give \$${customAmount.toStringAsFixed(2)}",
                                        ),
                                      ),
                                      
                                      if (customAmount > balance)
                                        Padding(
                                          padding: const EdgeInsets.only(top: 8),
                                          child: Text(
                                            'Add funds to your account to make donations',
                                            style: AppTextStyles.bodySmall.copyWith(
                                              color: AppColors.warningText,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      
                                      const SizedBox(height: 12),
                                      
                                      ElevatedButton(
                                        style: AppButtonStyles.primaryButton
                                            .copyWith(
                                          backgroundColor:
                                              MaterialStateProperty.all(
                                                  Colors.green),
                                        ),
                                        onPressed: _navigateToCashOut,
                                        child: const Text('Cash Out'),
                                      ),
                                    ],
                                  ),
                                ),
                                
                                if (showShareIcons) ...[
                                  const SizedBox(height: 20),
                                  _socialShareRow(),
                                ],
                                
                                const SizedBox(height: 20),
                                SizedBox(
                                  width: double.infinity,
                                  child: ElevatedButton(
                                      style: AppButtonStyles.primaryButton,
                                      onPressed: _load,
                                      child: const Text('Refresh')),
                                ),
                              ],
                            ),
                          ),
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}