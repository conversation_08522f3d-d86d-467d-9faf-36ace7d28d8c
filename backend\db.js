const Database = require('better-sqlite3');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { nanoid } = require('nanoid'); // For generating unique IDs

// Initialize database
const db = new Database(path.join(__dirname, 'hashtagdollar.db'));

// ---------------- Initialize tables ----------------
function init() {
  // Users table with Stripe Connect fields
  db.prepare(`
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      email TEXT UNIQUE,
      password TEXT,
      username TEXT UNIQUE,
      display_name TEXT,
      full_name TEXT,
      card_number TEXT,
      card_expiry_month INTEGER,
      card_expiry_year INTEGER,
      card_cvc TEXT,
      balance INTEGER DEFAULT 0,
      profile_picture BLOB,
      country TEXT,
      state TEXT,
      category TEXT,
      stripe_account_id TEXT,
      stripe_onboarded INTEGER DEFAULT 0,
      stripe_details_submitted INTEGER DEFAULT 0,
      created_at INTEGER
    )
  `).run();

  // Create donations table if not exists
  db.prepare(`
    CREATE TABLE IF NOT EXISTS donations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      fromUsername TEXT NOT NULL,
      toUsername TEXT NOT NULL,
      originalAmountCents INTEGER  NULL,
      amountCents INTEGER NOT NULL,
      feeCents INTEGER  NULL,
      success_url TEXT,
      cancel_url TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `).run();

  // Donation history
  db.prepare(`
    CREATE TABLE IF NOT EXISTS donation_history (
      id TEXT PRIMARY KEY,
      from_username TEXT,
      to_username TEXT,
      amount_cents INTEGER,
      created_at INTEGER
    )
  `).run();

  // Transactions table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS transactions (
      id TEXT PRIMARY KEY,
      from_user TEXT,
      to_user TEXT,
      amount INTEGER,
      type TEXT,
      note TEXT,
      created_at INTEGER
    )
  `).run();

  // Platform table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS platform (
      id INTEGER PRIMARY KEY CHECK (id=1),
      balance INTEGER DEFAULT 0
    )
  `).run();

  // Payments table (running balance per user)
  db.prepare(`
    CREATE TABLE IF NOT EXISTS payments (
      username TEXT PRIMARY KEY,
      amount INTEGER DEFAULT 0
    )
  `).run();

  // Payment history table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS payment_history (
      id TEXT PRIMARY KEY,
      username TEXT,
      amount INTEGER,
      session_id TEXT,
      created_at INTEGER
    )
  `).run();

  // Create favorite_givers table if not exists
  db.prepare(`
    CREATE TABLE IF NOT EXISTS favorite_givers (
      user_id TEXT NOT NULL,
      giver_id TEXT NOT NULL,
      PRIMARY KEY (user_id, giver_id),
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (giver_id) REFERENCES users(id) ON DELETE CASCADE
    )
  `).run();

  // Create payouts table with enhanced tracking
  db.prepare(`
    CREATE TABLE IF NOT EXISTS payouts (
      id TEXT PRIMARY KEY,
      username TEXT NOT NULL,
      amount_cents INTEGER NOT NULL,
      stripe_account_id TEXT,
      stripe_transfer_id TEXT,
      status TEXT DEFAULT 'pending',
      error_message TEXT,
      timestamp INTEGER,
      FOREIGN KEY (username) REFERENCES users(username) ON DELETE CASCADE
    )
  `).run();

  // ---------------- Insert default records ----------------
  const platformExists = db.prepare('SELECT count(*) as c FROM platform').get();
  if (platformExists.c === 0) db.prepare('INSERT INTO platform(id,balance) VALUES (1,0)').run();

  const userCount = db.prepare('SELECT COUNT(*) as c FROM users').get().c;
  if (userCount === 0) prepopulateTestData();
}

// ---------------- Prepopulate test data ----------------
function prepopulateTestData() {
  const now = Date.now();

  // Insert 10 sample users
  const insertUser = db.prepare(`
    INSERT INTO users (id, email, password, username, display_name, balance, created_at)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `);

  const insertFavoriteGiver = db.prepare(`
    INSERT INTO favorite_givers (user_id, giver_id)
    VALUES (?, ?)
  `);

  const insertTransaction = db.prepare(`
    INSERT INTO transactions (id, from_user, to_user, amount, type, note, created_at)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `);

  const insertDonation = db.prepare(`
    INSERT INTO donation_history (id, from_username, to_username, amount_cents, created_at)
    VALUES (?, ?, ?, ?, ?)
  `);

  const insertPayment = db.prepare(`
    INSERT INTO payments (username, amount)
    VALUES (?, ?)
  `);

  const insertPaymentHistory = db.prepare(`
    INSERT INTO payment_history (id, username, amount, session_id, created_at)
    VALUES (?, ?, ?, ?, ?)
  `);

  // // Insert users first
  // const insertUsers = db.transaction(() => {
  //   for (let i = 1; i <= 10; i++) {
  //     const id = nanoid();
  //     const email = `user${i}@example.com`;
  //     const password = `password${i}`;
  //     const username = `#$user${i}`;
  //     const displayName = `User ${i}`;
  //     const balance = 1000; // $10.00
  //     insertUser.run(id, email, password, username, displayName, balance, now);
  //     insertPayment.run(username, balance);
  //     insertPaymentHistory.run(uuidv4(), username, balance, `session-${i}`, now);
  //   }
  // });

  // insertUsers();

  // After users are inserted, add favorite givers
  const insertFavoriteGiversTransaction = db.transaction(() => {
    for (let i = 1; i <= 10; i++) {
      // Ensure the userId and giverId are both valid before inserting
      const userId = db.prepare('SELECT id FROM users WHERE username = ?').get(`#${i}`);
      const giverId = db.prepare('SELECT id FROM users WHERE username = ?').get(`#${(i % 10) + 1}`);
      
      // Check if both IDs were found, otherwise skip
      if (userId && giverId) {
        insertFavoriteGiver.run(userId.id, giverId.id);
      } else {
        console.log(`Skipping favorite giver insert for user#${i} and giver#${(i % 10) + 1}`);
      }
    }
  });

  insertFavoriteGiversTransaction();

  const insertTransactionsAndDonations = db.transaction(() => {
    for (let i = 1; i <= 10; i++) {
      const fromUser = `#$user${i}`;
      const toUser = `#$user${(i % 10) + 1}`;
      const amount = 25 * (i % 4 + 1); // $0.25, $0.50, $0.75, $1.00
      const note = `Test transaction ${i}`;
      const transactionId = nanoid();
      const donationId = uuidv4();
      const createdAt = now - (10 - i) * 1000 * 60;

      insertTransaction.run(transactionId, fromUser, toUser, amount, 'donation', note, createdAt);
      insertDonation.run(donationId, fromUser, toUser, amount, createdAt);
    }
  });

  insertTransactionsAndDonations();

  console.log('✅ Default tables populated with sample users, favorite givers, transactions, donations, payments, and payment history.');
}

// ---------------- Helper functions ----------------
function getUserIdByUsername(username) {
  const row = db.prepare('SELECT id FROM users WHERE username = ?').get(username);
  return row ? row.id : null;
}

function findUserByUsername(username) {
  return db.prepare('SELECT * FROM users WHERE username = ?').get(username);
}

// ---------------- Payment history ----------------
function recordPayment(username, amount, sessionId) {
  try {
    if (!username) throw new Error('Username is required');
    if (!sessionId) throw new Error('Session ID is required');

    const now = Date.now();
    const existing = db.prepare('SELECT 1 FROM payment_history WHERE session_id = ?').get(sessionId);
    if (existing) return { success: false, error: 'Payment already recorded for this session_id' };

    db.prepare(`
      INSERT INTO payment_history (id, username, amount, session_id, created_at)
      VALUES (?, ?, ?, ?, ?)
    `).run(uuidv4(), username, amount, sessionId, now);

    const row = db.prepare('SELECT amount FROM payments WHERE username = ?').get(username);
    if (row) {
      db.prepare('UPDATE payments SET amount = amount + ? WHERE username = ?').run(amount, username);
    } else {
      db.prepare('INSERT INTO payments (username, amount) VALUES (?, ?)').run(username, amount);
    }

    return { success: true, username, amount, sessionId, createdAt: now };
  } catch (err) {
    console.error('Error recording payment:', err);
    return { success: false, error: err.message };
  }
}

function getPaymentHistory(username) {
  return db.prepare('SELECT * FROM payment_history WHERE username = ? ORDER BY created_at DESC').all(username);
}

function getUserBalance(username) {
  const row = db.prepare('SELECT amount FROM payments WHERE username = ?').get(username);
  return row ? row.amount : 0;
}

// ---------------- Record donation ----------------
function recordDonation(fromUsername, toUsername, amountCents) {
  db.prepare(`
    INSERT INTO donation_history (id, from_username, to_username, amount_cents, created_at)
    VALUES (?, ?, ?, ?, ?)
  `).run(uuidv4(), fromUsername, toUsername, amountCents, Date.now());
}

module.exports = {
  db,
  init,
  recordDonation,
  getUserIdByUsername,
  recordPayment,
  getPaymentHistory,
  getUserBalance,
  findUserByUsername
};
