import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class PaymentCancelScreen extends StatelessWidget {
  const PaymentCancelScreen({super.key});

  @override
  Widget build(BuildContext context) {
    Fluttertoast.showToast(msg: "Payment Cancelled");

    return Scaffold(
      appBar: AppBar(
        title: const Text("Payment Cancelled"),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.cancel, color: Colors.red, size: 80),
            const SizedBox(height: 20),
            const Text(
              "Your payment was cancelled.",
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 20),
            ),
            const SizedBox(height: 30),
            ElevatedButton(
              onPressed: () {
                Navigator.pushReplacementNamed(context, '/profile', arguments: {'username': '#DemoUser'});
              },
              child: const Text("Go back to Profile"),
            )
          ],
        ),
      ),
    );
  }
}
