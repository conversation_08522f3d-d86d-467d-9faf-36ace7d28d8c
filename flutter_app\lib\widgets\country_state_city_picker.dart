import 'package:flutter/material.dart';
import 'package:country_state_city/country_state_city.dart' as csc;
import '../constants/theme_constants.dart';

class CountryStateCityPicker extends StatefulWidget {
  final String? initialCountry;
  final String? initialState;
  final String? initialCity;
  final Function(String? country, String? state, String? city) onChanged;
  final bool enabled;

  const CountryStateCityPicker({
    super.key,
    this.initialCountry,
    this.initialState,
    this.initialCity,
    required this.onChanged,
    this.enabled = true,
  });

  @override
  State<CountryStateCityPicker> createState() => _CountryStateCityPickerState();
}

class _CountryStateCityPickerState extends State<CountryStateCityPicker> {
  List<csc.Country> countries = [];
  List<csc.State> states = [];
  List<csc.City> cities = [];
  bool isLoading = true;

  String? selectedCountry;
  String? selectedState;
  String? selectedCity;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  @override
  void didUpdateWidget(CountryStateCityPicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialCountry != oldWidget.initialCountry ||
        widget.initialState != oldWidget.initialState ||
        widget.initialCity != oldWidget.initialCity) {
      // Update initial values and reload dependent data
      _applyInitialValues();
    }
  }

  Future<void> _initializeData() async {
    try {
      final allCountries = await csc.getAllCountries();
      // Remove duplicates by name to ensure unique dropdown values
      final uniqueCountries = <String, csc.Country>{};
      for (final country in allCountries) {
        if (!uniqueCountries.containsKey(country.name)) {
          uniqueCountries[country.name] = country;
        }
      }
      countries = uniqueCountries.values.toList();

      // Apply initial values after data is loaded
      await _applyInitialValues();
    } catch (e) {
      debugPrint('Error loading countries: $e');
    } finally {
      if (mounted) {
        setState(() => isLoading = false);
      }
    }
  }

  Future<void> _applyInitialValues() async {
    if (widget.initialCountry != null) {
      selectedCountry = widget.initialCountry;
      await _loadStatesForCountry(widget.initialCountry);
    }

    if (widget.initialState != null) {
      selectedState = widget.initialState;
      await _loadCitiesForState(widget.initialState);
    }

    if (widget.initialCity != null) {
      selectedCity = widget.initialCity;
    }

    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _loadStatesForCountry(String? countryName) async {
    if (countryName == null) {
      setState(() => states = []);
      return;
    }

    try {
      final country = countries.where((c) => c.name == countryName).firstOrNull;
      if (country != null) {
        final allStates = await csc.getStatesOfCountry(country.isoCode);
        // Remove duplicates by name
        final uniqueStates = <String, csc.State>{};
        for (final state in allStates) {
          if (!uniqueStates.containsKey(state.name)) {
            uniqueStates[state.name] = state;
          }
        }
        states = uniqueStates.values.toList();
      } else {
        states = [];
      }
    } catch (e) {
      debugPrint('Error loading states: $e');
      states = [];
    }

    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _loadCitiesForState(String? stateName) async {
    if (stateName == null) {
      setState(() => cities = []);
      return;
    }

    try {
      final state = states.where((s) => s.name == stateName).firstOrNull;
      if (state != null) {
        final country =
            countries.where((c) => c.isoCode == state.countryCode).firstOrNull;
        if (country != null) {
          final allCities =
              await csc.getStateCities(country.isoCode, state.isoCode);
          // Remove duplicates by name
          final uniqueCities = <String, csc.City>{};
          for (final city in allCities) {
            if (!uniqueCities.containsKey(city.name)) {
              uniqueCities[city.name] = city;
            }
          }
          cities = uniqueCities.values.toList();
        } else {
          cities = [];
        }
      } else {
        cities = [];
      }
    } catch (e) {
      debugPrint('Error loading cities: $e');
      cities = [];
    }

    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        // Country Dropdown
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: DropdownButtonFormField<String>(
            value: selectedCountry,
            decoration: AppInputDecorations.primaryInput(
              hintText: 'Country',
            ),
            style: AppTextStyles.inputText,
            dropdownColor: AppColors.roseGold,
            items: countries
                .map((country) => DropdownMenuItem<String>(
                      value: country.name,
                      child: Text(country.name, style: AppTextStyles.inputText),
                    ))
                .toList(),
            onChanged: widget.enabled
                ? (value) {
                    setState(() {
                      selectedCountry = value;
                      selectedState = null;
                      selectedCity = null;
                      states = [];
                      cities = [];
                    });
                    _loadStatesForCountry(value);
                    widget.onChanged(
                        selectedCountry, selectedState, selectedCity);
                  }
                : null,
          ),
        ),

        // State Dropdown
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: DropdownButtonFormField<String>(
            value: selectedState,
            decoration: AppInputDecorations.primaryInput(
              hintText: 'State/Province',
            ),
            style: AppTextStyles.inputText,
            dropdownColor: AppColors.roseGold,
            items: states
                .map((state) => DropdownMenuItem<String>(
                      value: state.name,
                      child: Text(state.name, style: AppTextStyles.inputText),
                    ))
                .toList(),
            onChanged: widget.enabled
                ? (value) {
                    setState(() {
                      selectedState = value;
                      selectedCity = null;
                      cities = [];
                    });
                    _loadCitiesForState(value);
                    widget.onChanged(
                        selectedCountry, selectedState, selectedCity);
                  }
                : null,
          ),
        ),

        // City Dropdown
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: DropdownButtonFormField<String>(
            value: selectedCity,
            decoration: AppInputDecorations.primaryInput(
              hintText: 'City',
            ),
            style: AppTextStyles.inputText,
            dropdownColor: AppColors.roseGold,
            items: cities
                .map((city) => DropdownMenuItem<String>(
                      value: city.name,
                      child: Text(city.name, style: AppTextStyles.inputText),
                    ))
                .toList(),
            onChanged: widget.enabled
                ? (value) {
                    setState(() => selectedCity = value);
                    widget.onChanged(
                        selectedCountry, selectedState, selectedCity);
                  }
                : null,
          ),
        ),
      ],
    );
  }
}
