# 🚀 Quick Start - Test Environment

## 🎯 **Start Testing in 5 Minutes**

### **Step 1: Start Backend Server**
```bash
cd backend
npm install
node migrate-database.js
npm start
```

**Expected output:**
```
✅ Database migration completed successfully!
Server running on port 4242
```

### **Step 2: Start Flutter App**
```bash
# In a new terminal
cd flutter_app
flutter pub get
flutter run -d chrome
```

**Expected:** Browser opens with your app at `http://localhost:3000`

### **Step 3: Quick Test**
1. **Register a new user** (any test email)
2. **Add $10** using test card: `4242 4242 4242 4242`
3. **Make a $0.25 donation** to another user
4. **Verify instant balance updates**

---

## 🧪 **Test Credit Cards (Stripe Test Mode)**

### **Successful Payments:**
- **Card:** `4242 4242 4242 4242`
- **Expiry:** Any future date (e.g., `12/25`)
- **CVC:** Any 3 digits (e.g., `123`)
- **ZIP:** Any 5 digits (e.g., `12345`)

### **Failed Payments (for testing errors):**
- **Declined:** `4000 0000 0000 0002`
- **Insufficient funds:** `4000 0000 0000 9995`

---

## 🔧 **Set Up Webhooks (Optional for Basic Testing)**

**Only needed for cash out testing:**

1. **Go to:** [Stripe Dashboard](https://dashboard.stripe.com) (Test mode)
2. **Developers → Webhooks → Add endpoint**
3. **URL:** `http://localhost:4242/api/payments/webhook`
4. **Events:** `checkout.session.completed`
5. **Copy webhook secret** to `backend/.env`

---

## ✅ **What Works Without Webhooks:**
- ✅ User registration
- ✅ Adding funds
- ✅ Instant donations
- ✅ Balance updates
- ✅ Stripe Connect account creation

## ⚠️ **What Needs Webhooks:**
- Cash out status updates
- Payment confirmation (backup)

---

## 🎯 **Ready to Test!**

Your test environment is configured with:
- ✅ **Stripe test keys** (safe to use)
- ✅ **Local database** (won't affect production)
- ✅ **Test webhooks** (optional)
- ✅ **All new features** (balance donations, cash out)

**Start testing and let me know if you hit any issues!** 🚀