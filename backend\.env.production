# 🚀 PRODUCTION ENVIRONMENT CONFIGURATION
# Use this when deploying to live servers

# Server Configuration
PORT=4242
NODE_ENV=production
JWT_SECRET=REPLACE_WITH_SUPER_SECURE_RANDOM_STRING_32_CHARS_PLUS

# Frontend URL (your actual domain)
FRONTEND_URL=https://hashtagdollars.com

# Stripe LIVE Keys (⚠️ KEEP THESE SECRET!)
STRIPE_SECRET_KEY=sk_live_YOUR_LIVE_SECRET_KEY_HERE
STRIPE_PUBLISHABLE_KEY=pk_live_YOUR_LIVE_PUBLISHABLE_KEY_HERE

# Stripe Webhook Secrets (from Stripe Dashboard)
STRIPE_WEBHOOK_SECRET=whsec_YOUR_LIVE_WEBHOOK_SECRET_HERE
STRIPE_CONNECT_WEBHOOK_SECRET=whsec_YOUR_LIVE_CONNECT_WEBHOOK_SECRET_HERE

# Database (production database path or URL)
DATABASE_PATH=./hashtagdollar_production.db