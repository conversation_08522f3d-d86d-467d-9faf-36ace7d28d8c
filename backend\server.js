require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { init } = require('./db');
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const paymentRoutes = require('./routes/payments');
const stripeConnectRoutes = require('./routes/stripe-connect');

const app = express();

// Initialize database
init();

// Enable CORS for all origins
app.use(cors());

// Body parsing
// Use raw body for Stripe webhooks, JSON for everything else
app.use((req, res, next) => {
  if (req.originalUrl === '/api/payments/webhook' || req.originalUrl === '/api/stripe-connect/webhook') {
    express.raw({ type: 'application/json' })(req, res, next);
  } else {
    express.json()(req, res, next);
  }
});

// API routes
app.use('/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/stripe-connect', stripeConnectRoutes);

// Public web pages for donations, top-ups, or simple static pages
app.use('/u', express.static(__dirname + '/web_public'));

// Catch-all route for undefined paths (optional, for web friendly URLs)
app.use((req, res) => {
  res.status(404).send('Not Found');
});

// Start server
const PORT = process.env.PORT || 4242;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
