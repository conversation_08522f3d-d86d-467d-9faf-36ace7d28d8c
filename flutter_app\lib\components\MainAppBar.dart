import 'package:flutter/material.dart';
import 'dart:typed_data';
import 'dart:convert';

import '../screens/cashout_screen.dart';
import '../screens/topup_screen.dart';
import '../screens/userprofile_screen.dart';
import '../services/api.dart';

class MainAppBar extends AppBar {
  final String loggedInUsername;
  final VoidCallback? onRefresh;
  final String headerTitle;

  MainAppBar({
    super.key,
    required this.loggedInUsername,
    this.onRefresh,
    required this.headerTitle,
  });

  @override
  State<MainAppBar> createState() => _MainAppBarState();
}

class _MainAppBarState extends State<MainAppBar> {
  Uint8List? _profileImageBytes;

  @override
  void initState() {
    super.initState();
    _loadProfileImage();
  }

  Future<void> _loadProfileImage() async {
    try {
      final profile = await Api.getUserByUsername(widget.loggedInUsername);
      if (profile != null) {
        final profilePictureUrl = profile['profile_picture']?.toString();
        if (profilePictureUrl != null && profilePictureUrl.isNotEmpty) {
          if (profilePictureUrl.startsWith('data:image')) {
            final base64Data = profilePictureUrl.split(',')[1];
            final bytes = base64Decode(base64Data);
            setState(() => _profileImageBytes = bytes);
          }
        }
      }
    } catch (e) {
      // <========== logs were here, add again if required for debug =========>
    }
  }

  Future<void> _refresh() async {
    if (widget.onRefresh != null) {
      widget.onRefresh!();
    } else {
      // Default refresh behavior - reload profile image
      await _loadProfileImage();
    }
  }

  Future<void> _logout() async {
    await Api.logout();
    if (mounted) {
      Navigator.pushReplacementNamed(context, '/landing');
    }
  }

  void _goToTopUp() {
    print("🔹 Navigating to TopUpScreen for ${widget.loggedInUsername}");
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => TopupScreen(username: widget.loggedInUsername),
      ),
    );
  }

  void _goToUserProfile() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => UserProfileScreen(username: widget.loggedInUsername),
      ),
    );
  }

  void _goToCashOut() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => CashOutScreen(username: widget.loggedInUsername),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      automaticallyImplyLeading: widget.headerTitle.contains('Hashtag'),
      title: Row(
        children: [
          Image.asset('assets/logo.png', height: 50),
          const SizedBox(width: 4),
          Text(widget.headerTitle),
        ],
      ),
      actions: [
        IconButton(icon: const Icon(Icons.refresh), onPressed: _refresh),
        IconButton(
          icon: const Icon(Icons.account_balance_wallet),
          tooltip: 'Add Funds',
          onPressed: _goToTopUp,
        ),
        IconButton(
          icon: const Icon(Icons.money),
          tooltip: 'Cash Out',
          onPressed: _goToCashOut,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Tooltip(
            message: 'Profile',
            child: GestureDetector(
              onTap: _goToUserProfile,
              child: CircleAvatar(
                radius: 16,
                backgroundColor: Colors.white,
                backgroundImage: _profileImageBytes != null
                    ? MemoryImage(_profileImageBytes!) as ImageProvider
                    : null,
                child: _profileImageBytes == null
                    ? const Icon(Icons.account_circle,
                        color: Colors.purple, size: 20)
                    : null,
              ),
            ),
          ),
        ),
        IconButton(
          icon: const Icon(Icons.logout),
          tooltip: 'Logout',
          onPressed: _logout,
        ),
      ],
    );
  }
}
