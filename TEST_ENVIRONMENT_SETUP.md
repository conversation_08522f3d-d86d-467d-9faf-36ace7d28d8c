# 🧪 Test Environment Setup Guide

## 🎯 **Quick Start - Test Locally**

### **Step 1: Set Up ngrok (for webhooks)**

Since <PERSON><PERSON> needs a public URL, we'll use ngrok to expose your local server:

```bash
# Install ngrok
npm install -g ngrok

# In a new terminal, expose your local server
ngrok http 4242
```

**Copy the HTTPS URL** from ngrok output (e.g., `https://abc123.ngrok.io`)

### **Step 2: Set Up Stripe Test Webhooks**

1. **Go to Stripe Dashboard** → Switch to **Test mode** (toggle in top left)
2. **Go to Developers → Webhooks**
3. **Click "Add endpoint"**

#### **Add Payment Webhook:**
- **URL:** `https://your-ngrok-url.ngrok.io/api/payments/webhook`
- **Description:** `Local testing - payments`
- **Events:** Select `checkout.session.completed`
- **Click "Create destination"**
- **Copy the webhook secret** (starts with `whsec_`)

#### **Add Connect Webhook:**
- **Click "Add endpoint" again**
- **URL:** `https://your-ngrok-url.ngrok.io/api/stripe-connect/webhook`
- **Description:** `Local testing - connect`
- **Events:** Select these events:
  - `account.updated`
  - `transfer.created` 
  - `transfer.paid`
  - `transfer.failed`
- **Click "Create destination"**
- **Copy the webhook secret** (starts with `whsec_`)

### **Step 3: Update Webhook Secrets**

Update your `backend/.env` file with the webhook secrets:

```bash
# Replace these with your actual webhook secrets from Step 2
STRIPE_WEBHOOK_SECRET=whsec_your_payment_webhook_secret_here
STRIPE_CONNECT_WEBHOOK_SECRET=whsec_your_connect_webhook_secret_here
```

### **Step 4: Start the Backend**

```bash
cd backend

# Install dependencies (if not done already)
npm install

# Run database migrations
node migrate-database.js

# Start the server
npm start
```

You should see:
```
✅ Database migration completed successfully!
Server running on port 4242
```

### **Step 5: Start the Flutter App**

```bash
cd flutter_app

# Get dependencies (if not done already)
flutter pub get

# Start the web app
flutter run -d chrome
```

The app should open at `http://localhost:3000` (or similar)

---

## 🧪 **Testing the Complete Flow**

### **Test 1: User Registration & Stripe Connect**

1. **Register a new user** (use test email like `<EMAIL>`)
2. **Check backend logs** - should see:
   ```
   ✅ Created Stripe Connect account acct_xxx for new user #$testuser
   ```
3. **Verify in Stripe Dashboard** → Connect → Accounts (should see new test account)

### **Test 2: Add Funds**

1. **Click "Add Funds"** in the app
2. **Enter amount** (e.g., $10.00)
3. **Click "Add Funds"** → Should open Stripe Checkout
4. **Use test card:** `4242 4242 4242 4242`
   - **Expiry:** Any future date (e.g., 12/25)
   - **CVC:** Any 3 digits (e.g., 123)
   - **ZIP:** Any 5 digits (e.g., 12345)
5. **Complete payment** → Should redirect back to app
6. **Check balance** → Should show $10.00

### **Test 3: Balance-Based Donations**

1. **Find another user** in the user list (or create a second account)
2. **Click on their profile**
3. **Enter donation amount** (e.g., $0.25)
4. **Click "Give"** → Should be instant!
5. **Check balances** → Donor should have $9.75, recipient should have $0.25

### **Test 4: Stripe Connect Verification**

1. **Go to Cash Out screen**
2. **Click "Verify Now"** → Should open Stripe onboarding
3. **Use test information:**
   - **Business type:** Individual
   - **Country:** United States
   - **First/Last name:** Test User
   - **DOB:** 01/01/1990
   - **Address:** 123 Test St, Test City, CA, 12345
   - **SSN:** *********** (test SSN)
   - **Phone:** (*************
4. **Complete verification** → Should redirect back to app
5. **Check status** → Should show "Verification Complete"

### **Test 5: Cash Out**

1. **Ensure you have ≥ $5.00** in balance
2. **Go to Cash Out screen**
3. **Enter amount** (e.g., $5.00)
4. **Click "Cash Out"** → Should initiate transfer
5. **Check Stripe Dashboard** → Transfers (should see test transfer)
6. **Check payout history** → Should show "processing" status

---

## 🔧 **Troubleshooting**

### **Backend Won't Start**
```bash
# Check if port is in use
netstat -an | grep 4242

# Kill process if needed (Windows)
taskkill /f /im node.exe

# Kill process if needed (Mac/Linux)  
pkill -f node
```

### **Flutter App Won't Connect**
1. **Check backend is running** on `http://localhost:4242`
2. **Verify .env file** has correct `SERVER_URL=http://localhost:4242`
3. **Restart Flutter app** after .env changes

### **Stripe Webhooks Not Working**
1. **Use ngrok** for local webhook testing:
   ```bash
   # Install ngrok
   npm install -g ngrok
   
   # Expose local server
   ngrok http 4242
   
   # Use the https URL for webhooks (e.g., https://abc123.ngrok.io)
   ```
2. **Update webhook URLs** in Stripe Dashboard to use ngrok URL

### **Database Issues**
```bash
# Reset test database
cd backend
rm hashtagdollar_test.db
node migrate-database.js
```

---

## 📊 **Test Data Verification**

### **Check Database**
```bash
cd backend
sqlite3 hashtagdollar.db

# Check users
SELECT username, balance, stripe_account_id, stripe_onboarded FROM users;

# Check donations
SELECT * FROM donation_history ORDER BY created_at DESC LIMIT 5;

# Check payouts
SELECT * FROM payouts ORDER BY timestamp DESC LIMIT 5;

# Exit
.quit
```

### **Check Stripe Dashboard**
- **Payments** → Should see test payments
- **Connect → Accounts** → Should see test accounts
- **Connect → Transfers** → Should see test transfers

---

## 🎯 **Success Criteria**

Your test environment is working correctly if:

✅ **Users can register** and Stripe Connect accounts are created  
✅ **Users can add funds** using test credit cards  
✅ **Donations are instant** and balances update immediately  
✅ **Stripe verification works** with test data  
✅ **Cash outs initiate** real Stripe transfers  
✅ **Webhooks update** payout status correctly  

---

## 🚀 **Ready for Production?**

Once all tests pass:

1. **Copy `.env.production` files** to `.env`
2. **Update with live Stripe keys**
3. **Set up live webhooks** with your production domain
4. **Deploy to production servers**
5. **Test with real money** (small amounts first!)

**Need help with any step?** Let me know! 🎯