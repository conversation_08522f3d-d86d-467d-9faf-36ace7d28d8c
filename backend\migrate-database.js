require('dotenv').config();
const { db } = require('./db');

console.log('🔄 Running database migrations...\n');

// Migration 1: Add Stripe Connect fields to users table
console.log('1. Adding Stripe Connect fields to users table...');
try {
  // Check if columns already exist
  const userInfo = db.prepare("PRAGMA table_info(users)").all();
  const hasStripeAccountId = userInfo.some(col => col.name === 'stripe_account_id');
  const hasStripeOnboarded = userInfo.some(col => col.name === 'stripe_onboarded');
  const hasStripeDetailsSubmitted = userInfo.some(col => col.name === 'stripe_details_submitted');

  if (!hasStripeAccountId) {
    db.prepare('ALTER TABLE users ADD COLUMN stripe_account_id TEXT').run();
    console.log('   ✅ Added stripe_account_id column');
  } else {
    console.log('   ✅ stripe_account_id column already exists');
  }

  if (!hasStripeOnboarded) {
    db.prepare('ALTER TABLE users ADD COLUMN stripe_onboarded INTEGER DEFAULT 0').run();
    console.log('   ✅ Added stripe_onboarded column');
  } else {
    console.log('   ✅ stripe_onboarded column already exists');
  }

  if (!hasStripeDetailsSubmitted) {
    db.prepare('ALTER TABLE users ADD COLUMN stripe_details_submitted INTEGER DEFAULT 0').run();
    console.log('   ✅ Added stripe_details_submitted column');
  } else {
    console.log('   ✅ stripe_details_submitted column already exists');
  }

} catch (err) {
  console.log('   ❌ Error adding Stripe fields to users table:', err.message);
}

// Migration 2: Update payouts table with new fields
console.log('\n2. Updating payouts table schema...');
try {
  // Check if columns already exist
  const payoutInfo = db.prepare("PRAGMA table_info(payouts)").all();
  const hasStatus = payoutInfo.some(col => col.name === 'status');
  const hasStripeTransferId = payoutInfo.some(col => col.name === 'stripe_transfer_id');
  const hasErrorMessage = payoutInfo.some(col => col.name === 'error_message');

  if (!hasStatus) {
    db.prepare('ALTER TABLE payouts ADD COLUMN status TEXT DEFAULT "pending"').run();
    console.log('   ✅ Added status column');
  } else {
    console.log('   ✅ status column already exists');
  }

  if (!hasStripeTransferId) {
    db.prepare('ALTER TABLE payouts ADD COLUMN stripe_transfer_id TEXT').run();
    console.log('   ✅ Added stripe_transfer_id column');
  } else {
    console.log('   ✅ stripe_transfer_id column already exists');
  }

  if (!hasErrorMessage) {
    db.prepare('ALTER TABLE payouts ADD COLUMN error_message TEXT').run();
    console.log('   ✅ Added error_message column');
  } else {
    console.log('   ✅ error_message column already exists');
  }

  // Update stripe_account column name if it exists
  const hasOldStripeAccount = payoutInfo.some(col => col.name === 'stripe_account');
  if (hasOldStripeAccount && !payoutInfo.some(col => col.name === 'stripe_account_id')) {
    // SQLite doesn't support renaming columns directly, so we'll add the new column
    db.prepare('ALTER TABLE payouts ADD COLUMN stripe_account_id TEXT').run();
    // Copy data from old column to new column
    db.prepare('UPDATE payouts SET stripe_account_id = stripe_account WHERE stripe_account IS NOT NULL').run();
    console.log('   ✅ Added stripe_account_id column and migrated data');
  }

} catch (err) {
  console.log('   ❌ Error updating payouts table:', err.message);
}

// Migration 3: Add profile fields to users table
console.log('\n3. Adding profile fields to users table...');
try {
  // Check if columns already exist
  const userInfo = db.prepare("PRAGMA table_info(users)").all();
  const hasBio = userInfo.some(col => col.name === 'bio');
  const hasPhone = userInfo.some(col => col.name === 'phone');
  const hasUrl1 = userInfo.some(col => col.name === 'url1');
  const hasUrl2 = userInfo.some(col => col.name === 'url2');
  const hasUrl3 = userInfo.some(col => col.name === 'url3');
  const hasUrl4 = userInfo.some(col => col.name === 'url4');
  const hasUrl5 = userInfo.some(col => col.name === 'url5');

  if (!hasBio) {
    db.prepare('ALTER TABLE users ADD COLUMN bio TEXT').run();
    console.log('   ✅ Added bio column');
  } else {
    console.log('   ✅ bio column already exists');
  }

  if (!hasPhone) {
    db.prepare('ALTER TABLE users ADD COLUMN phone TEXT').run();
    console.log('   ✅ Added phone column');
  } else {
    console.log('   ✅ phone column already exists');
  }

  if (!hasUrl1) {
    db.prepare('ALTER TABLE users ADD COLUMN url1 TEXT').run();
    console.log('   ✅ Added url1 column');
  } else {
    console.log('   ✅ url1 column already exists');
  }

  if (!hasUrl2) {
    db.prepare('ALTER TABLE users ADD COLUMN url2 TEXT').run();
    console.log('   ✅ Added url2 column');
  } else {
    console.log('   ✅ url2 column already exists');
  }

  if (!hasUrl3) {
    db.prepare('ALTER TABLE users ADD COLUMN url3 TEXT').run();
    console.log('   ✅ Added url3 column');
  } else {
    console.log('   ✅ url3 column already exists');
  }

  if (!hasUrl4) {
    db.prepare('ALTER TABLE users ADD COLUMN url4 TEXT').run();
    console.log('   ✅ Added url4 column');
  } else {
    console.log('   ✅ url4 column already exists');
  }

  if (!hasUrl5) {
    db.prepare('ALTER TABLE users ADD COLUMN url5 TEXT').run();
    console.log('   ✅ Added url5 column');
  } else {
    console.log('   ✅ url5 column already exists');
  }

} catch (err) {
  console.log('   ❌ Error adding profile fields to users table:', err.message);
}

// Migration 4: Add city column to users table
console.log('\n4. Adding city column to users table...');
try {
  // Check if column already exists
  const userInfo = db.prepare("PRAGMA table_info(users)").all();
  const hasCity = userInfo.some(col => col.name === 'city');

  if (!hasCity) {
    db.prepare('ALTER TABLE users ADD COLUMN city TEXT').run();
    console.log('   ✅ Added city column');
  } else {
    console.log('   ✅ city column already exists');
  }

} catch (err) {
  console.log('   ❌ Error adding city column to users table:', err.message);
}

// Migration 5: Create password reset tokens table
console.log('\n5. Creating password reset tokens table...');
try {
  // Check if table exists
  const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='password_reset_tokens'").get();

  if (!tables) {
    // Table doesn't exist, it should be created by db.js but let's verify
    console.log('   ✅ Password reset tokens table should be created by db.js');
  } else {
    console.log('   ✅ Password reset tokens table exists');
  }

} catch (err) {
  console.log('   ❌ Error checking password reset tokens table:', err.message);
}

// Migration 6: Verify tables are working
console.log('\n6. Verifying database integrity...');
try {
  // Test users table
  const userTest = db.prepare('SELECT stripe_account_id, stripe_onboarded, stripe_details_submitted FROM users LIMIT 1').get();
  console.log('   ✅ Users table with Stripe fields is working');

  // Test payouts table
  const payoutTest = db.prepare('SELECT status, stripe_transfer_id, error_message FROM payouts LIMIT 1').get();
  console.log('   ✅ Payouts table with new fields is working');

  console.log('\n🎉 Database migration completed successfully!');

} catch (err) {
  console.log('   ❌ Database verification failed:', err.message);
}

console.log('\n📊 Current table schemas:');
console.log('\nUsers table columns:');
const userColumns = db.prepare("PRAGMA table_info(users)").all();
userColumns.forEach(col => {
  console.log(`   - ${col.name} (${col.type})`);
});

console.log('\nPayouts table columns:');
const payoutColumns = db.prepare("PRAGMA table_info(payouts)").all();
payoutColumns.forEach(col => {
  console.log(`   - ${col.name} (${col.type})`);
});

console.log('\n✅ Migration script completed!');