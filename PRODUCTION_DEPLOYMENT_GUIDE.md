# 🚀 Production Deployment Guide - Hashtag Dollar

## 🎯 **Step 1: Configure Environment Variables**

### **Backend Environment Setup**

Create or update your `backend/.env` file:

```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_live_...  # Your live Stripe secret key
STRIPE_PUBLISHABLE_KEY=pk_live_...  # Your live Stripe publishable key
STRIPE_WEBHOOK_SECRET=whsec_...  # Webhook secret from Stripe Dashboard
STRIPE_CONNECT_WEBHOOK_SECRET=whsec_...  # Connect webhook secret

# Server Configuration
PORT=4242
NODE_ENV=production
JWT_SECRET=your-super-secure-jwt-secret-here

# Frontend URL (for redirects)
FRONTEND_URL=https://your-domain.com

# Database (if using external DB)
DATABASE_URL=your-database-url-if-needed
```

### **Flutter Environment Setup**

Update `flutter_app/.env`:

```bash
# Backend API URL
SERVER_URL=https://your-api-domain.com

# Stripe Publishable Key (for frontend)
STRIPE_PUBLISHABLE_KEY=pk_live_...
```

---

## 🎯 **Step 2: Set Up Stripe Webhooks**

### **In Stripe Dashboard:**

1. **Go to Developers → Webhooks**
2. **Click "Add endpoint"**
3. **Add these endpoints:**

#### **Payment Webhooks**
- **URL:** `https://your-api-domain.com/api/payments/webhook`
- **Events:** `checkout.session.completed`

#### **Connect Webhooks**
- **URL:** `https://your-api-domain.com/api/stripe-connect/webhook`
- **Events:** 
  - `account.updated`
  - `transfer.created`
  - `transfer.paid`
  - `transfer.failed`

4. **Copy webhook secrets** to your `.env` file

---

## 🎯 **Step 3: Test with Real Stripe Accounts**

### **Create Test Transfers**

```bash
# Test the donation flow
curl -X POST https://your-api-domain.com/api/users/donate \
  -H "Content-Type: application/json" \
  -d '{
    "fromUsername": "#$yourtest",
    "toUsername": "#$recipient",
    "amountCents": 25,
    "feeCents": 0
  }'

# Test Stripe account creation
curl -X POST https://your-api-domain.com/api/stripe-connect/create-account \
  -H "Content-Type: application/json" \
  -d '{
    "username": "#$yourtest",
    "email": "<EMAIL>"
  }'
```

---

## 🎯 **Step 4: Deploy Backend**

### **Option A: Deploy to Railway/Render/Heroku**

1. **Connect your GitHub repo**
2. **Set environment variables** in the platform dashboard
3. **Deploy from main branch**

### **Option B: Deploy to VPS/Cloud Server**

```bash
# On your server
git clone https://github.com/your-repo/hashtag-dollar.git
cd hashtag-dollar/backend

# Install dependencies
npm install --production

# Run database migrations
node migrate-database.js

# Start with PM2 (recommended)
npm install -g pm2
pm2 start server.js --name "hashtag-dollar-api"
pm2 startup
pm2 save
```

### **Nginx Configuration (if using VPS)**

```nginx
server {
    listen 80;
    server_name your-api-domain.com;
    
    location / {
        proxy_pass http://localhost:4242;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

---

## 🎯 **Step 5: Deploy Flutter App**

### **Web Deployment**

```bash
cd flutter_app

# Build for web
flutter build web --release

# Deploy to your hosting service
# Copy build/web/* to your web server
```

### **Mobile App Deployment**

#### **Android (Google Play)**
```bash
# Build Android App Bundle
flutter build appbundle --release

# Upload to Google Play Console
# File: build/app/outputs/bundle/release/app-release.aab
```

#### **iOS (App Store)**
```bash
# Build iOS app
flutter build ios --release

# Open in Xcode and archive for App Store
open ios/Runner.xcworkspace
```

---

## 🎯 **Step 6: Production Testing Checklist**

### **✅ Backend Testing**

```bash
# Test server health
curl https://your-api-domain.com/api/users

# Test donation with real money (small amount)
# Use your app to donate $0.25 between test accounts

# Test cash out with real bank account
# Complete Stripe verification and cash out $5.00
```

### **✅ Frontend Testing**

1. **Register new user** → Should create Stripe Connect account
2. **Add funds** → Should work with real payment
3. **Make donation** → Should be instant with balance update
4. **Complete verification** → Should enable cash out
5. **Cash out funds** → Should initiate real transfer

---

## 🎯 **Step 7: Monitor System Performance**

### **Set Up Monitoring**

#### **Backend Monitoring**
```javascript
// Add to server.js
const express = require('express');
const app = express();

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Error logging
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  // Send to monitoring service (e.g., Sentry)
});
```

#### **Database Monitoring**
```bash
# Check database size and performance
sqlite3 backend/hashtagdollar.db "
SELECT 
  COUNT(*) as total_users,
  SUM(balance) as total_balance,
  COUNT(CASE WHEN stripe_onboarded = 1 THEN 1 END) as verified_users
FROM users;
"
```

---

## 🎯 **Step 8: Launch Communication**

### **User Announcement Template**

```markdown
🎉 **Hashtag Dollar 2.0 is Here!**

**New Features:**
✅ **Instant Donations** - No more waiting for payments to process
✅ **Real Cash Outs** - Withdraw your earnings to your bank account
✅ **Enhanced Security** - Stripe-verified accounts for safe transfers

**What's Changed:**
- Donations now use your account balance (much faster!)
- Complete one-time verification to enable cash outs
- Improved user interface and experience

**Getting Started:**
1. Update your app to the latest version
2. Add funds to your account
3. Start giving and receiving instantly!

Questions? Contact <NAME_EMAIL>
```

---

## 🎯 **Step 9: Post-Launch Monitoring**

### **First 24 Hours Checklist**

- [ ] **Monitor server logs** for errors
- [ ] **Check donation success rate** (should be >99%)
- [ ] **Verify cash out processing** (1-2 business days)
- [ ] **Monitor user feedback** and support requests
- [ ] **Track key metrics:**
  - New user registrations
  - Donation volume
  - Cash out requests
  - Stripe verification completion rate

### **Key Metrics to Track**

```sql
-- Daily donation volume
SELECT 
  DATE(created_at/1000, 'unixepoch') as date,
  COUNT(*) as donations,
  SUM(amount_cents)/100.0 as total_amount
FROM donation_history 
WHERE created_at >= strftime('%s', 'now', '-7 days') * 1000
GROUP BY DATE(created_at/1000, 'unixepoch');

-- Verification completion rate
SELECT 
  COUNT(*) as total_users,
  COUNT(CASE WHEN stripe_onboarded = 1 THEN 1 END) as verified_users,
  ROUND(COUNT(CASE WHEN stripe_onboarded = 1 THEN 1 END) * 100.0 / COUNT(*), 2) as verification_rate
FROM users;
```

---

## 🚨 **Emergency Procedures**

### **If Donations Fail**
1. Check backend server status
2. Verify database connectivity
3. Check user balances in database
4. Review error logs

### **If Cash Outs Fail**
1. Check Stripe Connect webhook status
2. Verify user verification status
3. Check Stripe Dashboard for transfer status
4. Review payout error messages

### **Rollback Plan**
```bash
# If needed, rollback to previous version
git checkout previous-stable-tag
pm2 restart hashtag-dollar-api
```

---

## 🎉 **You're Ready to Launch!**

Your Hashtag Dollar platform is now production-ready with:

✅ **Instant balance-based donations**
✅ **Real Stripe Connect cash outs**  
✅ **Comprehensive monitoring**
✅ **Emergency procedures**
✅ **User communication plan**

**Next Action Items:**
1. Set up your production environment variables
2. Configure Stripe webhooks
3. Deploy backend and frontend
4. Run production tests
5. Launch! 🚀

Need help with any specific step? Let me know!