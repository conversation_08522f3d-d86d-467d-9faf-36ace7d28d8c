require('dotenv').config();
const { init, findUserByUsername } = require('./db');
const { db } = require('./db');

// Initialize database
init();

console.log('🧪 Testing Hashtag Dollar Implementations\n');

// Test 1: Check if database schema is updated
console.log('1. Testing Database Schema Updates...');
try {
  const user = db.prepare('SELECT stripe_account_id, stripe_onboarded, stripe_details_submitted FROM users LIMIT 1').get();
  console.log('✅ Database schema updated successfully');
  console.log('   Sample user Stripe fields:', user || 'No users found');
} catch (err) {
  console.log('❌ Database schema update failed:', err.message);
}

// Test 2: Check payouts table
console.log('\n2. Testing Payouts Table Schema...');
try {
  const payout = db.prepare('SELECT status, stripe_transfer_id, error_message FROM payouts LIMIT 1').get();
  console.log('✅ Payouts table schema updated successfully');
  console.log('   Sample payout fields:', payout || 'No payouts found');
} catch (err) {
  console.log('❌ Payouts table schema update failed:', err.message);
}

// Test 3: Test balance-based donation logic (simulation)
console.log('\n3. Testing Balance-Based Donation Logic...');
try {
  // Create test users if they don't exist
  const testDonor = '#$testdonor';
  const testRecipient = '#$testrecipient';
  
  let donor = findUserByUsername(testDonor);
  if (!donor) {
    db.prepare('INSERT INTO users (id, username, balance, created_at) VALUES (?, ?, ?, ?)').run(
      'test-donor-id', testDonor, 1000, Date.now()
    );
    donor = findUserByUsername(testDonor);
  }
  
  let recipient = findUserByUsername(testRecipient);
  if (!recipient) {
    db.prepare('INSERT INTO users (id, username, balance, created_at) VALUES (?, ?, ?, ?)').run(
      'test-recipient-id', testRecipient, 0, Date.now()
    );
    recipient = findUserByUsername(testRecipient);
  }

  console.log(`   Donor balance before: ${donor.balance} cents`);
  console.log(`   Recipient balance before: ${recipient.balance} cents`);

  // Simulate donation
  const donationAmount = 25; // $0.25
  const fee = 0; // No fee for test
  
  const donationResult = db.transaction(() => {
    // Check balance
    if (donor.balance < donationAmount) {
      throw new Error('Insufficient balance');
    }
    
    // Transfer funds
    db.prepare('UPDATE users SET balance = balance - ? WHERE username = ?').run(donationAmount, testDonor);
    db.prepare('UPDATE users SET balance = balance + ? WHERE username = ?').run(donationAmount, testRecipient);
    
    return true;
  })();

  const updatedDonor = findUserByUsername(testDonor);
  const updatedRecipient = findUserByUsername(testRecipient);

  console.log(`   Donor balance after: ${updatedDonor.balance} cents`);
  console.log(`   Recipient balance after: ${updatedRecipient.balance} cents`);
  console.log('✅ Balance-based donation logic working correctly');

} catch (err) {
  console.log('❌ Balance-based donation test failed:', err.message);
}

// Test 4: Check Stripe Connect environment
console.log('\n4. Testing Stripe Connect Configuration...');
if (process.env.STRIPE_SECRET_KEY) {
  console.log('✅ STRIPE_SECRET_KEY is configured');
} else {
  console.log('❌ STRIPE_SECRET_KEY is missing');
}

if (process.env.STRIPE_CONNECT_WEBHOOK_SECRET) {
  console.log('✅ STRIPE_CONNECT_WEBHOOK_SECRET is configured');
} else {
  console.log('⚠️  STRIPE_CONNECT_WEBHOOK_SECRET is missing (needed for production)');
}

console.log('\n🎉 Implementation testing completed!');
console.log('\n📋 Next Steps:');
console.log('1. Update Flutter app to use new donation API');
console.log('2. Add Stripe onboarding screens to Flutter app');
console.log('3. Test with real Stripe Connect accounts');
console.log('4. Set up webhook endpoints for production');