import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../services/api.dart';
import '../constants/theme_constants.dart';
import '../widgets/share_bottom_sheet.dart';
import 'userprofile_screen.dart';
import 'cashout_screen.dart';
import 'topup_screen.dart';

class PersonalProfileScreen extends StatefulWidget {
  final String username; // The logged-in user's username

  const PersonalProfileScreen({
    super.key,
    required this.username,
  });

  @override
  State<PersonalProfileScreen> createState() => _PersonalProfileScreenState();
}

class _PersonalProfileScreenState extends State<PersonalProfileScreen> {
  Map<String, dynamic> user = {};
  bool loading = true;

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  Future<void> _loadUserProfile() async {
    setState(() => loading = true);
    try {
      final userData = await Api.getUserByUsername(widget.username);
      if (userData != null) {
        setState(() {
          user = userData['user'] ?? userData;
          loading = false;
        });
      } else {
        setState(() => loading = false);
        Fluttertoast.showToast(msg: 'Error loading profile');
      }
    } catch (e) {
      setState(() => loading = false);
      Fluttertoast.showToast(msg: 'Error loading profile: $e');
    }
  }

  void _shareProfile() {
    if (user == null) return;

    final displayName = user!['display_name'] ?? user!['username'] ?? 'User';
    final username = user!['username'] ?? '';
    final bio = user!['bio'] ?? '';

    String shareText = 'Check out my profile on Hashtag Dollars! 🌟\n\n';
    if (bio.isNotEmpty) {
      shareText += '$bio\n\n';
    }
    shareText += 'Support me with micro-donations starting at just \$0.25!';

    // You can customize the URL structure based on your app's deep linking
    final shareUrl = 'https://hashtagdollars.com/profile/$username';

    ShareBottomSheet.show(
      context,
      shareText: shareText,
      shareUrl: shareUrl,
    );
  }

  void _navigateToEditProfile() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => UserProfileScreen(username: widget.username),
      ),
    ).then((_) => _loadUserProfile()); // Refresh after editing
  }

  void _navigateToCashOut() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => CashOutScreen(username: widget.username),
      ),
    );
  }

  void _navigateToTopUp() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => TopupScreen(username: widget.username),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final displayName = user['display_name'] ?? '';
    final shortHandle = widget.username.startsWith('#\$')
        ? widget.username
        : "#\${widget.username}";
    final balance = ((user['balance'] ?? 0) as int) / 100.0;

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Profile'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _navigateToEditProfile,
          ),
          // Share button will be added later
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              _shareProfile();
            },
          ),
        ],
      ),
      body: Container(
        color: AppColors.roseGold,
        child: loading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  Expanded(
                    child: Center(
                      child: ConstrainedBox(
                        constraints: const BoxConstraints(maxWidth: 800),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Left Column - Profile Info
                              Expanded(
                                flex: 1,
                                child: Column(
                                  children: [
                                    // Profile Picture
                                    _buildProfilePicture(),
                                    const SizedBox(height: 16),

                                    // User Info
                                    Text(
                                      shortHandle,
                                      style: AppTextStyles.title,
                                      textAlign: TextAlign.center,
                                    ),
                                    if (displayName.isNotEmpty) ...[
                                      const SizedBox(height: 8),
                                      Text(
                                        displayName,
                                        style: AppTextStyles.bodyLarge,
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                    const SizedBox(height: 16),

                                    // Balance
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16, vertical: 8),
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.2),
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: Text(
                                        'Balance: \$${balance.toStringAsFixed(2)}',
                                        style: AppTextStyles.bodyLarge.copyWith(
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    const SizedBox(height: 16),

                                    // Category
                                    if (user['category'] != null &&
                                        user['category']
                                            .toString()
                                            .isNotEmpty) ...[
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 12, vertical: 6),
                                        decoration: BoxDecoration(
                                          color: Colors.white.withOpacity(0.2),
                                          borderRadius:
                                              BorderRadius.circular(20),
                                        ),
                                        child: Text(
                                          user['category'].toString(),
                                          style: AppTextStyles.bodyMedium,
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                    ],

                                    // Bio (will be added later)
                                    // URLs (will be added later)
                                  ],
                                ),
                              ),

                              const SizedBox(width: 24),

                              // Right Column - Actions
                              Expanded(
                                flex: 1,
                                child: Column(
                                  children: [
                                    const SizedBox(height: 40),

                                    // Top Up Button
                                    SizedBox(
                                      width: double.infinity,
                                      child: ElevatedButton(
                                        style: AppButtonStyles.primaryButton,
                                        onPressed: _navigateToTopUp,
                                        child: const Text('Top Up'),
                                      ),
                                    ),

                                    const SizedBox(height: 16),

                                    // Cash Out Button
                                    SizedBox(
                                      width: double.infinity,
                                      child: ElevatedButton(
                                        style: AppButtonStyles.secondaryButton,
                                        onPressed: _navigateToCashOut,
                                        child: const Text('Cash Out'),
                                      ),
                                    ),

                                    const SizedBox(height: 16),

                                    // Edit Profile Button
                                    SizedBox(
                                      width: double.infinity,
                                      child: OutlinedButton(
                                        style: OutlinedButton.styleFrom(
                                          foregroundColor: Colors.white,
                                          side: const BorderSide(
                                              color: Colors.white),
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 32, vertical: 16),
                                        ),
                                        onPressed: _navigateToEditProfile,
                                        child: const Text('Edit Profile'),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildProfilePicture() {
    final profilePictureUrl = user['profile_picture']?.toString();

    if (profilePictureUrl != null && profilePictureUrl.isNotEmpty) {
      if (profilePictureUrl.startsWith('data:image')) {
        // Base64 image
        try {
          final base64Data = profilePictureUrl.split(',')[1];
          final bytes = base64Decode(base64Data);
          return CircleAvatar(
            radius: 60,
            backgroundImage: MemoryImage(bytes),
          );
        } catch (e) {
          return _buildDefaultAvatar();
        }
      } else {
        // Network image
        final imageUrl = profilePictureUrl.startsWith('http')
            ? profilePictureUrl
            : '${Api.base}$profilePictureUrl';

        return CircleAvatar(
          radius: 60,
          child: CachedNetworkImage(
            imageUrl: imageUrl,
            imageBuilder: (context, imageProvider) => CircleAvatar(
              radius: 60,
              backgroundImage: imageProvider,
            ),
            placeholder: (context, url) => const CircularProgressIndicator(),
            errorWidget: (context, url, error) => _buildDefaultAvatar(),
          ),
        );
      }
    }

    return _buildDefaultAvatar();
  }

  Widget _buildDefaultAvatar() {
    return const CircleAvatar(
      radius: 60,
      backgroundColor: Colors.grey,
      child: Icon(Icons.person, size: 60, color: Colors.white),
    );
  }
}
