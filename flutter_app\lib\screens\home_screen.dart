import 'package:flutter/material.dart';
import 'dart:typed_data';
import 'dart:convert';
import '../services/api.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'profile_screen.dart'; // Import ProfileScreen
import 'topup_screen.dart'; // Import TopUpScreen
import 'userprofile_screen.dart'; // Import UserProfileScreen
import 'cashout_screen.dart'; // Import CashOutScreen
import '../widgets/navigation_bar.dart';
import '../constants/theme_constants.dart';

class HomeScreen extends StatefulWidget {
  final String loggedInUsername; // logged-in user
  const HomeScreen({super.key, required this.loggedInUsername});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  List users = [];
  List filteredUsers = [];
  bool loading = true;
  Map<String, dynamic>? currentUserProfile;
  Uint8List? _profileImageBytes;

  int currentPage = 1;
  int itemsPerPage = -1; // Default to show all users
  final List<int> itemsPerPageOptions = [10, 50, 100, -1]; // -1 = all
  String searchQuery = "";

  @override
  void initState() {
    super.initState();
    print("🔹 Logged-in user on HomeScreen: ${widget.loggedInUsername}");
    _loadUsers();
    _loadCurrentUserProfile();
  }

  Future<void> _loadCurrentUserProfile() async {
    print('👤 Loading profile for user: ${widget.loggedInUsername}');

    try {
      final profile = await Api.getUserByUsername(widget.loggedInUsername);
      if (profile != null) {
        print('✅ Profile loaded successfully');
        setState(() {
          currentUserProfile = profile;
        });

        // Handle profile picture
        final profilePictureUrl = profile['profile_picture']?.toString();
        if (profilePictureUrl != null && profilePictureUrl.isNotEmpty) {
          if (profilePictureUrl.startsWith('data:image')) {
            // It's a base64 data URL, decode it
            try {
              final base64Data = profilePictureUrl.split(',')[1];
              final bytes = base64Decode(base64Data);
              setState(() => _profileImageBytes = bytes);
              print('🖼️ Profile picture loaded successfully');
            } catch (e) {
              print('❌ Error decoding profile picture: $e');
            }
          }
        }
      } else {
        print('⚠️ Profile data is null - user might not exist or server error');
      }
    } catch (e) {
      print('🚨 Error loading current user profile: $e');

      // Show user-friendly error message
      if (e.toString().contains('TimeoutException')) {
        print(
            '⏰ Network timeout - this might be due to slow connection or server issues');
        // You could show a snackbar here to inform the user
        // ScaffoldMessenger.of(context).showSnackBar(
        //   SnackBar(content: Text('Network timeout. Please check your connection.')),
        // );
      } else {
        print('🔍 Other error occurred while loading profile');
      }
    }
  }

  Future<void> _loadUsers() async {
    setState(() => loading = true);
    try {
      final u = await Api.fetchUsers();
      setState(() {
        users = u ?? [];
        _filterAndPaginate();
      });
    } catch (e) {
      Fluttertoast.showToast(msg: 'Unable to fetch users: $e');
    } finally {
      setState(() => loading = false);
    }
  }

  void _filterAndPaginate() {
    filteredUsers = users
        .where((u) =>
            (u['username'] ?? '')
                .toString()
                .toLowerCase()
                .contains(searchQuery.toLowerCase()) ||
            (u['display_name'] ?? '')
                .toString()
                .toLowerCase()
                .contains(searchQuery.toLowerCase()))
        .toList();

    if (currentPage > totalPages) currentPage = 1;
    setState(() {});
  }

  int get totalPages {
    if (itemsPerPage == -1) return 1;
    return (filteredUsers.length / itemsPerPage).ceil();
  }

  List get paginatedUsers {
    if (itemsPerPage == -1) return filteredUsers;
    final start = (currentPage - 1) * itemsPerPage;
    final end = start + itemsPerPage;
    return filteredUsers.sublist(
        start, end > filteredUsers.length ? filteredUsers.length : end);
  }

  void _changePage(int page) {
    if (page >= 1 && page <= totalPages) {
      setState(() => currentPage = page);
    }
  }

  Future<void> _refresh() async {
    await _loadUsers();
    await _loadCurrentUserProfile();
  }

  Future<void> _logout() async {
    await Api.logout();
    if (mounted) {
      Navigator.pushReplacementNamed(context, '/landing');
    }
  }

  void _goToTopUp() {
    print("🔹 Navigating to TopUpScreen for ${widget.loggedInUsername}");
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => TopupScreen(username: widget.loggedInUsername),
      ),
    );
  }

  void _goToUserProfile() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => UserProfileScreen(username: widget.loggedInUsername),
      ),
    );
  }

  void _goToCashOut() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => CashOutScreen(username: widget.loggedInUsername),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          actions: [
            IconButton(icon: const Icon(Icons.refresh), onPressed: _refresh),
            IconButton(
              icon: const Icon(Icons.account_balance_wallet),
              tooltip: 'Add Funds',
              onPressed: _goToTopUp,
            ),
            IconButton(
              icon: const Icon(Icons.money),
              tooltip: 'Cash Out',
              onPressed: _goToCashOut,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Tooltip(
                message: 'Profile',
                child: GestureDetector(
                  onTap: _goToUserProfile,
                  child: CircleAvatar(
                    radius: 16,
                    backgroundColor: Colors.white,
                    backgroundImage: _profileImageBytes != null
                        ? MemoryImage(_profileImageBytes!) as ImageProvider
                        : null,
                    child: _profileImageBytes == null
                        ? const Icon(Icons.account_circle,
                            color: Colors.purple, size: 20)
                        : null,
                  ),
                ),
              ),
            ),
            IconButton(
              icon: const Icon(Icons.logout),
              tooltip: 'Logout',
              onPressed: _logout,
            ),
          ],
        ),
        body: Container(
          color: AppColors.roseGold, // Rose Gold background
          child: Column(
            children: [
              Expanded(
                child: Center(
                  child: Container(
                    constraints: const BoxConstraints(maxWidth: 900),
                    padding: const EdgeInsets.all(16),
                    child: loading
                        ? const Center(child: CircularProgressIndicator())
                        : Column(
                            children: [
                              Container(
                                width: double.infinity,
                                color: Colors.white.withOpacity(0.2),
                                padding: const EdgeInsets.all(16),
                                child: Text(
                                  'Welcome, ${widget.loggedInUsername}',
                                  style: AppTextStyles
                                      .bodyLarge, // Enlarged from 18 to 18 (but using consistent styling)
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              const SizedBox(height: 12),
                              Row(
                                children: [
                                  Expanded(
                                    child: TextField(
                                      style: AppTextStyles.inputText,
                                      decoration:
                                          AppInputDecorations.primaryInput(
                                        hintText: "Search users...",
                                        suffixIcon: const Icon(Icons.search,
                                            color: Colors.white70),
                                      ),
                                      onChanged: (val) {
                                        searchQuery = val;
                                        _filterAndPaginate();
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  DropdownButton<int>(
                                    value: itemsPerPage,
                                    items: itemsPerPageOptions.map((e) {
                                      return DropdownMenuItem<int>(
                                        value: e,
                                        child:
                                            Text(e == -1 ? "All" : "First $e"),
                                      );
                                    }).toList(),
                                    onChanged: (val) {
                                      if (val != null) {
                                        setState(() {
                                          itemsPerPage = val;
                                          currentPage = 1;
                                          _filterAndPaginate();
                                        });
                                      }
                                    },
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              Expanded(
                                child: filteredUsers.isEmpty
                                    ? const Center(
                                        child: Text('No users found'))
                                    : ListView.builder(
                                        itemCount: paginatedUsers.length,
                                        itemBuilder: (ctx, i) {
                                          final u = paginatedUsers[i];
                                          final balance =
                                              ((u['balance'] ?? 0) as int) /
                                                  100.0;

                                          // Handle profile picture
                                          Widget? profileAvatar;
                                          final profilePicture =
                                              u['profile_picture']?.toString();
                                          if (profilePicture != null &&
                                              profilePicture.isNotEmpty) {
                                            if (profilePicture
                                                .startsWith('data:image')) {
                                              // Base64 image
                                              try {
                                                final base64Data =
                                                    profilePicture
                                                        .split(',')[1];
                                                final bytes =
                                                    base64Decode(base64Data);
                                                profileAvatar = CircleAvatar(
                                                  backgroundImage:
                                                      MemoryImage(bytes),
                                                  radius: 20,
                                                );
                                              } catch (e) {
                                                profileAvatar =
                                                    const CircleAvatar(
                                                  child: Icon(Icons.person),
                                                  radius: 20,
                                                );
                                              }
                                            } else if (profilePicture
                                                .startsWith('http')) {
                                              // Network image
                                              profileAvatar = CircleAvatar(
                                                backgroundImage: NetworkImage(
                                                    profilePicture),
                                                radius: 20,
                                              );
                                            } else {
                                              profileAvatar =
                                                  const CircleAvatar(
                                                child: Icon(Icons.person),
                                                radius: 20,
                                              );
                                            }
                                          } else {
                                            profileAvatar = const CircleAvatar(
                                              child: Icon(Icons.person),
                                              radius: 20,
                                            );
                                          }

                                          return Card(
                                            margin: const EdgeInsets.symmetric(
                                                vertical: 4, horizontal: 0),
                                            child: ListTile(
                                              leading: profileAvatar,
                                              title: Text(
                                                u['username'] ?? '',
                                                style: const TextStyle(
                                                    fontWeight:
                                                        FontWeight.bold),
                                              ),
                                              subtitle:
                                                  Text(u['display_name'] ?? ''),
                                              trailing: Text(
                                                  '\$${balance.toStringAsFixed(2)}'),
                                              onTap: () {
                                                final toUsername =
                                                    u['username'] ?? 'guest';
                                                print(
                                                    "🔹 Opening ProfileScreen for: $toUsername");

                                                Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                    builder: (_) =>
                                                        ProfileScreen(
                                                      fromUsername: widget
                                                          .loggedInUsername,
                                                      toUsername: toUsername,
                                                    ),
                                                  ),
                                                );
                                              },
                                            ),
                                          );
                                        },
                                      ),
                              ),
                            ],
                          ),
                  ),
                ),
              ),
              // const AppFooter(), No need footer on home screen
            ],
          ),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: _goToTopUp,
          child: const Icon(Icons.account_balance_wallet),
        ),
      ),
    );
  }
}
