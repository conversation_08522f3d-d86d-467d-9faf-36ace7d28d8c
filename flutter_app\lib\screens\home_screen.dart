import 'package:flutter/material.dart';
import 'package:hashtag_dollars/components/CustomIconButton.dart';
import 'package:provider/provider.dart';
import 'dart:typed_data';
import 'dart:convert';
import '../services/api.dart';
import '../services/balance_provider.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../widgets/footer.dart';
import 'give_cashOut_screen.dart'; // Import ProfileScreen
import 'topup_screen.dart'; // Import TopUpScreen
import 'cashout_screen.dart'; // Import CashOutScreen
import '../widgets/navigation_bar.dart';
import '../constants/theme_constants.dart';
import '../widgets/enhanced_share_widget.dart';

class HomeScreen extends StatefulWidget {
  final String loggedInUsername; // logged-in user
  const HomeScreen({super.key, required this.loggedInUsername});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  List users = [];
  List filteredUsers = [];
  bool loading = true;
  Map<String, dynamic>? currentUserProfile;
  Uint8List? _profileImageBytes;
  bool _hasAutoNavigatedToProfile =
      false; // Flag to prevent multiple navigations

  int currentPage = 1;
  int itemsPerPage = -1; // Default to show all users
  final List<int> itemsPerPageOptions = [10, 50, 100, -1]; // -1 = all
  String searchQuery = "";

  @override
  void initState() {
    super.initState();
    print("🔹 Logged-in user on HomeScreen: ${widget.loggedInUsername}");
    _loadUsers();
    _loadCurrentUserProfile();
  }

  Future<void> _loadCurrentUserProfile() async {
    // <========== logs were here, add again if required for debug =========>

    try {
      final profile = await Api.getUserByUsername(widget.loggedInUsername);
      if (profile != null) {
        // <========== logs were here, add again if required for debug =========>
        if (mounted) {
          setState(() {
            currentUserProfile = profile;
          });
        }

        // Handle profile picture
        final profilePictureUrl = profile['profile_picture']?.toString();
        if (profilePictureUrl != null && profilePictureUrl.isNotEmpty) {
          if (profilePictureUrl.startsWith('data:image')) {
            // It's a base64 data URL, decode it
            try {
              final base64Data = profilePictureUrl.split(',')[1];
              final bytes = base64Decode(base64Data);
              if (mounted) setState(() => _profileImageBytes = bytes);
              // <========== logs were here, add again if required for debug =========>
            } catch (e) {
              // <========== logs were here, add again if required for debug =========>
            }
          }
        }

        // Auto-navigate to profile view screen after login
        if (!_hasAutoNavigatedToProfile) {
          _hasAutoNavigatedToProfile = true;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              Navigator.pushNamed(context, '/userprofileview',
                  arguments: widget.loggedInUsername);
            }
          });
        }
      } else {
        // <========== logs were here, add again if required for debug =========>
      }
    } catch (e) {
      // <========== logs were here, add again if required for debug =========>

      // Show user-friendly error message
      if (e.toString().contains('TimeoutException')) {
        print(
            '⏰ Network timeout - this might be due to slow connection or server issues');
        // You could show a snackbar here to inform the user
        // ScaffoldMessenger.of(context).showSnackBar(
        //   SnackBar(content: Text('Network timeout. Please check your connection.')),
        // );
      } else {
        // <========== logs were here, add again if required for debug =========>
      }
    }
  }

  Future<void> _loadUsers() async {
    if (mounted) setState(() => loading = true);
    try {
      final u = await Api.fetchUsers();
      if (mounted) {
        setState(() {
          users = u ?? [];
          _filterAndPaginate();
        });
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'Unable to fetch users: $e');
    } finally {
      if (mounted) setState(() => loading = false);
    }
  }

  void _filterAndPaginate() {
    filteredUsers = users
        .where((u) =>
            u['username'] != widget.loggedInUsername &&
            ((u['username'] ?? '')
                    .toString()
                    .toLowerCase()
                    .contains(searchQuery.toLowerCase()) ||
                (u['display_name'] ?? '')
                    .toString()
                    .toLowerCase()
                    .contains(searchQuery.toLowerCase())))
        .toList();

    if (currentPage > totalPages) currentPage = 1;
    if (mounted) setState(() {});
  }

  int get totalPages {
    if (itemsPerPage == -1) return 1;
    return (filteredUsers.length / itemsPerPage).ceil();
  }

  List get paginatedUsers {
    if (itemsPerPage == -1) return filteredUsers;
    final start = (currentPage - 1) * itemsPerPage;
    final end = start + itemsPerPage;
    return filteredUsers.sublist(
        start, end > filteredUsers.length ? filteredUsers.length : end);
  }

  void _changePage(int page) {
    if (page >= 1 && page <= totalPages) {
      setState(() => currentPage = page);
    }
  }

  Future<void> _refresh() async {
    await _loadUsers();
    await _loadCurrentUserProfile();
  }

  Future<void> _logout() async {
    await Api.logout();
    if (mounted) {
      Navigator.pushReplacementNamed(context, '/landing');
    }
  }

  void _goToTopUp() {
    print("🔹 Navigating to TopUpScreen for ${widget.loggedInUsername}");
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => TopupScreen(username: widget.loggedInUsername),
      ),
    );
  }

  void _goToUserProfile() {
    Navigator.pushNamed(context, '/userprofileview',
        arguments: widget.loggedInUsername);
  }

  void _goToCashOut() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => CashOutScreen(username: widget.loggedInUsername),
      ),
    );
  }

  void _showShareBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.cardBackground,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => EnhancedShareWidget(
        contentId: widget.loggedInUsername,
        contentText: currentUserProfile?['bio'] ??
            currentUserProfile?['display_name'] ??
            'Check out my profile',
        contentType: 'profile',
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: Scaffold(
        appBar: AppBar(
          leading: Builder(
            builder: (context) {
              final screenWidth = MediaQuery.of(context).size.width;
              final isWideScreen =
                  screenWidth > 600; // Tablet/desktop breakpoint

              return isWideScreen
                  ? Row(
                      children: [
                        // Logo
                        Image.asset(
                          'assets/logo.png',
                          height: 40,
                        ),
                        const SizedBox(width: 8),
                        // Name - only show on wide screens
                        Text('Hashtag Dollars #\$',
                            style: AppTextStyles.bodyMedium
                                .copyWith(color: Colors.black)),
                      ],
                    )
                  : Image.asset(
                      'assets/logo.png',
                      height: 40,
                    ); // Only logo on mobile
            },
          ),
          automaticallyImplyLeading: false,
          actions: [
            // Centered actions using Expanded
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                      icon: const Icon(Icons.refresh), onPressed: _refresh),
                  IconButton(
                    icon: const Icon(Icons.share),
                    tooltip: 'Share Profile',
                    onPressed: _showShareBottomSheet,
                  ),
                  IconButton(
                    icon: const Icon(Icons.account_balance_wallet),
                    tooltip: 'Add Funds',
                    onPressed: _goToTopUp,
                  ),
                  IconButton(
                    icon: const Icon(Icons.money),
                    tooltip: 'Cash Out',
                    onPressed: _goToCashOut,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 2),
                    child: Tooltip(
                      message: 'Profile',
                      child: GestureDetector(
                        onTap: _goToUserProfile,
                        child: CircleAvatar(
                          radius: 16,
                          backgroundColor: Colors.white,
                          backgroundImage: _profileImageBytes != null
                              ? MemoryImage(_profileImageBytes!)
                                  as ImageProvider
                              : null,
                          child: _profileImageBytes == null
                              ? const Icon(Icons.account_circle,
                                  color: Colors.purple, size: 20)
                              : null,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Logout button on the right
            IconButton(
              icon: const Icon(Icons.logout),
              tooltip: 'Logout',
              onPressed: _logout,
            ),
          ],
        ),
        body: Container(
          height: double.maxFinite,
          color: AppColors.roseGold, // Rose Gold background
          child: Column(
            children: [
              Expanded(
                child: Center(
                  child: Container(
                    constraints: const BoxConstraints(maxWidth: 900),
                    padding: const EdgeInsets.all(16),
                    child: loading
                        ? const Center(child: CircularProgressIndicator())
                        : Column(
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  color: Colors.white.withOpacity(0.1),
                                ),
                                width: double.infinity,
                                padding: const EdgeInsets.all(16),
                                child: Consumer<BalanceProvider>(
                                  builder: (context, balanceProvider, child) {
                                    return Column(
                                      children: [
                                        Row(
                                          children: [
                                            // Show Current User profile picture
                                            CircleAvatar(
                                              radius: 20,
                                              backgroundImage:
                                                  _profileImageBytes != null
                                                      ? MemoryImage(
                                                          _profileImageBytes!)
                                                      : null,
                                              child: _profileImageBytes == null
                                                  ? const Icon(Icons.person)
                                                  : null,
                                            ),
                                            const SizedBox(width: 8),
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    ' ${widget.loggedInUsername}',
                                                    style:
                                                        AppTextStyles.bodyLarge,
                                                    textAlign: TextAlign.left,
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Text(
                                                    'Balance: \$${balanceProvider.balance.toStringAsFixed(2)}',
                                                    style: AppTextStyles
                                                        .bodyMedium
                                                        .copyWith(
                                                      color: Colors.amberAccent,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            IconButton(
                                              onPressed: _showShareBottomSheet,
                                              icon: const Icon(Icons.share),
                                            ),
                                          ],
                                        ),
                                      ],
                                    );
                                  },
                                ),
                              ),
                              const SizedBox(height: 12),
                              Row(
                                children: [
                                  Expanded(
                                    child: TextField(
                                      style: AppTextStyles.inputText,
                                      decoration:
                                          AppInputDecorations.primaryInput(
                                        hintText: "Search users...",
                                        suffixIcon: const Icon(Icons.search,
                                            color: Colors.white70),
                                      ),
                                      onChanged: (val) {
                                        searchQuery = val;
                                        _filterAndPaginate();
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  DropdownButton<int>(
                                    value: itemsPerPage,
                                    items: itemsPerPageOptions.map((e) {
                                      return DropdownMenuItem<int>(
                                        value: e,
                                        child:
                                            Text(e == -1 ? "All" : "First $e"),
                                      );
                                    }).toList(),
                                    onChanged: (val) {
                                      if (val != null) {
                                        setState(() {
                                          itemsPerPage = val;
                                          currentPage = 1;
                                          _filterAndPaginate();
                                        });
                                      }
                                    },
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              Expanded(
                                child: filteredUsers.isEmpty
                                    ? const Center(
                                        child: Text('No users found'))
                                    : ListView.builder(
                                        itemCount: paginatedUsers.length,
                                        itemBuilder: (ctx, i) {
                                          // Dont include current logged in User

                                          final u = paginatedUsers[i];

                                          // Handle profile picture
                                          Widget? profileAvatar;
                                          final profilePicture =
                                              u['profile_picture']?.toString();
                                          if (profilePicture != null &&
                                              profilePicture.isNotEmpty) {
                                            if (profilePicture
                                                .startsWith('data:image')) {
                                              // Base64 image
                                              try {
                                                final base64Data =
                                                    profilePicture
                                                        .split(',')[1];
                                                final bytes =
                                                    base64Decode(base64Data);
                                                profileAvatar = CircleAvatar(
                                                  backgroundImage:
                                                      MemoryImage(bytes),
                                                  radius: 20,
                                                );
                                              } catch (e) {
                                                profileAvatar =
                                                    const CircleAvatar(
                                                  child: Icon(Icons.person),
                                                  radius: 20,
                                                );
                                              }
                                            } else if (profilePicture
                                                .startsWith('http')) {
                                              // Network image
                                              profileAvatar = CircleAvatar(
                                                backgroundImage: NetworkImage(
                                                    profilePicture),
                                                radius: 20,
                                              );
                                            } else {
                                              profileAvatar =
                                                  const CircleAvatar(
                                                child: Icon(Icons.person),
                                                radius: 20,
                                              );
                                            }
                                          } else {
                                            profileAvatar = const CircleAvatar(
                                              child: Icon(Icons.person),
                                              radius: 20,
                                            );
                                          }

                                          return Card(
                                            margin: const EdgeInsets.symmetric(
                                                vertical: 4, horizontal: 0),
                                            child: ListTile(
                                              leading: profileAvatar,
                                              title: Text(
                                                u['username'] ?? '',
                                                style: const TextStyle(
                                                    fontWeight:
                                                        FontWeight.bold),
                                              ),
                                              subtitle:
                                                  Text(u['display_name'] ?? ''),
                                              // Balance display removed from search results for privacy
                                              onTap: () {
                                                final toUsername =
                                                    u['username'] ?? 'guest';
                                                print(
                                                    "🔹 Opening ProfileScreen for: $toUsername");

                                                Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                    builder: (_) =>
                                                        GiveCashOutScreen(
                                                      fromUsername: widget
                                                          .loggedInUsername,
                                                      toUsername: toUsername,
                                                    ),
                                                  ),
                                                );
                                              },
                                            ),
                                          );
                                        },
                                      ),
                              ),
                            ],
                          ),
                  ),
                ),
              ),
              const AppFooter(),
            ],
          ),
        ),
        // floatingActionButton: FloatingActionButton(
        //   onPressed: _goToTopUp,
        //   child: const Icon(Icons.account_balance_wallet),
        // ),
      ),
    );
  }
}
