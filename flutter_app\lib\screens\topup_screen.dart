import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:hashtag_dollars/services/balance_provider.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/api.dart';
import 'package:csv/csv.dart';
import 'dart:convert';
import 'package:universal_html/html.dart' as html;
import 'package:excel/excel.dart'; // for Excel export
import '../constants/theme_constants.dart';
import 'webview_payment_screen.dart';

class TopupScreen extends StatefulWidget {
  final String username;
  const TopupScreen({super.key, required this.username});

  @override
  State<TopupScreen> createState() => _TopupScreenState();
}

class _TopupScreenState extends State<TopupScreen> {
  final amountCtrl = TextEditingController(text: '2.00');
  bool loading = false;
  List<dynamic> transactions = [];

  // Pagination variables
  int currentPage = 0;
  int pageSize = 10;
  final pageSizeOptions = [5, 10, 50, 100, -1];
  List<dynamic> currentTransactions = [];

  @override
  void initState() {
    super.initState();
    _load();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh balance when returning to this screen (e.g., from payment success)
    _load();
  }

  Future<void> _load() async {
    setState(() => loading = true);
    try {
      final tx = await Api.getPaymentHistory(widget.username);

      setState(() {
        transactions = tx ?? [];
        currentPage = 0;
        _updateCurrentTransactions();
      });

      // Refresh balance from provider
      final balanceProvider =
          Provider.of<BalanceProvider>(context, listen: false);
      await balanceProvider.refreshBalance();
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error: $e');
    } finally {
      setState(() => loading = false);
    }
  }

  void _updateCurrentTransactions() {
    if (pageSize == -1) {
      currentTransactions = List.from(transactions);
    } else {
      final start = currentPage * pageSize;
      final end = start + pageSize;
      currentTransactions = transactions.sublist(
          start, end > transactions.length ? transactions.length : end);
    }
  }

  void _prevPage() {
    if (currentPage > 0) {
      setState(() {
        currentPage--;
        _updateCurrentTransactions();
      });
    }
  }

  void _nextPage() {
    final maxPage =
        pageSize == -1 ? 0 : (transactions.length / pageSize).ceil() - 1;
    if (currentPage < maxPage) {
      setState(() {
        currentPage++;
        _updateCurrentTransactions();
      });
    }
  }

  void _changePageSize(int? value) {
    setState(() {
      pageSize = value ?? 10;
      currentPage = 0;
      _updateCurrentTransactions();
    });
  }

  void _exportCSV() {
    List<List<String>> csvData = [
      ['session_id', 'amount', 'created_at'],
      ...transactions.map((t) => [
            t['session_id'] ?? '',
            ((t['amount'] ?? 0) / 100.0).toStringAsFixed(2),
            t['created_at'] != null
                ? DateTime.fromMillisecondsSinceEpoch(t['created_at'])
                    .toLocal()
                    .toString()
                : ''
          ])
    ];

    String csv = const ListToCsvConverter().convert(csvData);
    if (kIsWeb) {
      final bytes = utf8.encode(csv);
      final blob = html.Blob([bytes]);
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.AnchorElement(href: url)
        ..setAttribute("download", "payment_history.csv")
        ..click();
      html.Url.revokeObjectUrl(url);
    } else {
      Fluttertoast.showToast(msg: "CSV export not implemented for non-web");
    }
  }

  void _exportExcel() {
    var excel = Excel.createExcel();
    Sheet sheet = excel['Sheet1'];
    sheet.appendRow(['session_id', 'amount', 'created_at']);
    for (var t in transactions) {
      sheet.appendRow([
        t['session_id'] ?? '',
        ((t['amount'] ?? 0) / 100.0).toStringAsFixed(2),
        t['created_at'] != null
            ? DateTime.fromMillisecondsSinceEpoch(t['created_at'])
                .toLocal()
                .toString()
            : ''
      ]);
    }
    final bytes = excel.encode();
    if (kIsWeb && bytes != null) {
      final blob = html.Blob([bytes]);
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.AnchorElement(href: url)
        ..setAttribute("download", "payment_history.xlsx")
        ..click();
      html.Url.revokeObjectUrl(url);
    } else {
      Fluttertoast.showToast(msg: "Excel export not implemented for non-web");
    }
  }

  Future<void> _startTopup() async {
    final val = double.tryParse(amountCtrl.text);
    if (val == null || val < 2.0) {
      Fluttertoast.showToast(msg: 'Minimum top-up is \$2.00');
      return;
    }

    setState(() => loading = true);
    try {
      final frontendBaseUrl =
          kIsWeb ? '${Uri.base.origin}' : 'hashtagdollars://payment-success';

      final res = await Api.startTopup(
        username: widget.username,
        amountCents: (val * 100).toInt(),
        successUrl: frontendBaseUrl,
        cancelUrl: frontendBaseUrl,
      );

      final url = res?['url'] as String?;
      if (url != null) {
        if (kIsWeb) {
          final ok = await launchUrl(Uri.parse(url),
              mode: LaunchMode.externalApplication);
          if (!ok) {
            Fluttertoast.showToast(msg: 'Cannot open Stripe Checkout URL');
          }
        } else {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => WebViewPaymentScreen(
                paymentUrl: url,
                successUrl: frontendBaseUrl,
                cancelUrl: frontendBaseUrl,
              ),
            ),
          );

          if (result?['success'] == true) {
            Fluttertoast.showToast(msg: 'Payment completed successfully!');
            _load();
          }
        }
      } else {
        Fluttertoast.showToast(msg: 'Unable to create checkout session');
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error: $e');
    } finally {
      setState(() => loading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final displayU = widget.username;

    return Scaffold(
      appBar: AppBar(
        title: Text('Add Funds ($displayU)'),
      ),
      body: Container(
        color: AppColors.roseGold, // Rose Gold background
        child: Column(
          children: [
            // Logo
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset('assets/logo.png', height: 50),
                // Title
                const Text(
                  'Hashtag Dollars',
                  style: AppTextStyles.title,
                ),
              ],
            ),
            const SizedBox(height: 10),
            Expanded(
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(
                    maxWidth: 600, // limit width for web/desktop
                  ),
                  child: loading
                      ? const Center(child: CircularProgressIndicator())
                      : RefreshIndicator(
                          onRefresh: _load,
                          child: SingleChildScrollView(
                            physics: const AlwaysScrollableScrollPhysics(),
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Consumer<BalanceProvider>(
                                  builder: (context, balanceProvider, child) {
                                    return Text(
                                      'Balance: \$${balanceProvider.balance.toStringAsFixed(2)}',
                                      style: AppTextStyles.title,
                                    );
                                  },
                                ),
                                const SizedBox(height: 12),
                                TextField(
                                  controller: amountCtrl,
                                  style: AppTextStyles.inputText,
                                  decoration: AppInputDecorations.primaryInput(
                                    hintText: 'Amount (USD)',
                                  ),
                                  keyboardType:
                                      const TextInputType.numberWithOptions(
                                          decimal: true),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Note: You will receive 90% of the amount you pay due to platform fees.',
                                  style: AppTextStyles.bodySmall.copyWith(
                                    color: AppColors.warningText,
                                    fontStyle: FontStyle.italic,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 4),
                                SizedBox(
                                  width: double.infinity,
                                  child: ElevatedButton(
                                      style: AppButtonStyles.primaryButton,
                                      onPressed: _startTopup,
                                      child: const Text('Add Funds')),
                                ),
                                const SizedBox(height: 16),
                                // refresh button
                                SizedBox(
                                  width: double.infinity,
                                  child: ElevatedButton(
                                      style: AppButtonStyles.secondaryButton,
                                      onPressed: _load,
                                      child: const Text('Refresh')),
                                ),
                                // Row(
                                //   children: [
                                //     const Text('Show: '),
                                //     DropdownButton<int>(
                                //       value: pageSize,
                                //       items: pageSizeOptions
                                //           .map((e) => DropdownMenuItem(
                                //               value: e,
                                //               child: Text(e == -1
                                //                   ? 'All'
                                //                   : e.toString())))
                                //           .toList(),
                                //       onChanged: _changePageSize,
                                //     ),
                                //     const Spacer(),
                                //     ElevatedButton(
                                //         onPressed: _exportCSV,
                                //         child: const Text('Export CSV')),
                                //     const SizedBox(width: 8),
                                //     ElevatedButton(
                                //         onPressed: _exportExcel,
                                //         child: const Text('Export Excel')),
                                //   ],
                                // ),
                                // const SizedBox(height: 12),
                                // SingleChildScrollView(
                                //   scrollDirection: Axis.horizontal,
                                //   child: DataTable(
                                //     columns: const [
                                //       DataColumn(label: Text('Session ID')),
                                //       DataColumn(label: Text('Amount')),
                                //       DataColumn(label: Text('Created At')),
                                //     ],
                                //     rows: currentTransactions
                                //         .map((t) => DataRow(cells: [
                                //               DataCell(
                                //                   Text(t['session_id'] ?? '')),
                                //               DataCell(Text(
                                //                   ((t['amount'] ?? 0) / 100.0)
                                //                       .toStringAsFixed(2))),
                                //               DataCell(Text(t['created_at'] !=
                                //                       null
                                //                   ? DateTime
                                //                           .fromMillisecondsSinceEpoch(
                                //                               t['created_at'])
                                //                       .toLocal()
                                //                       .toString()
                                //                   : '')),
                                //             ]))
                                //         .toList(),
                                //   ),
                                // ),
                                // const SizedBox(height: 12),
                                // Row(
                                //   mainAxisAlignment: MainAxisAlignment.center,
                                //   children: [
                                //     ElevatedButton(
                                //         onPressed: _prevPage,
                                //         child: const Text('Prev')),
                                //     const SizedBox(width: 12),
                                //     Text('Page ${currentPage + 1}'),
                                //     const SizedBox(width: 12),
                                //     ElevatedButton(
                                //         onPressed: _nextPage,
                                //         child: const Text('Next')),
                                //   ],
                                // ),
                              ],
                            ),
                          ),
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
