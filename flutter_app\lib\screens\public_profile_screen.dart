import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../services/api.dart';
import '../constants/theme_constants.dart';
import '../widgets/share_bottom_sheet.dart';
import 'profile_screen.dart';
import 'cashout_screen.dart';

class PublicProfileScreen extends StatefulWidget {
  final String username; // The profile being viewed
  final String? viewerUsername; // The user viewing the profile

  const PublicProfileScreen({
    super.key,
    required this.username,
    this.viewerUsername,
  });

  @override
  State<PublicProfileScreen> createState() => _PublicProfileScreenState();
}

class _PublicProfileScreenState extends State<PublicProfileScreen> {
  Map<String, dynamic> user = {};
  bool loading = true;

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  Future<void> _loadUserProfile() async {
    setState(() => loading = true);
    try {
      final userData = await Api.getUserByUsername(widget.username);
      if (userData != null) {
        setState(() {
          user = userData['user'] ?? userData;
          loading = false;
        });
      } else {
        setState(() => loading = false);
        Fluttertoast.showToast(msg: 'User not found');
      }
    } catch (e) {
      setState(() => loading = false);
      Fluttertoast.showToast(msg: 'Error loading profile: $e');
    }
  }

  void _shareProfile() {
    if (user == null) return;

    final displayName = user!['display_name'] ?? user!['username'] ?? 'User';
    final username = user!['username'] ?? '';
    final bio = user!['bio'] ?? '';

    String shareText = 'Check out $displayName on Hashtag Dollars! 🌟\n\n';
    if (bio.isNotEmpty) {
      shareText += '$bio\n\n';
    }
    shareText += 'Support them with micro-donations starting at just \$0.25!';

    // You can customize the URL structure based on your app's deep linking
    final shareUrl = 'https://hashtagdollars.com/profile/$username';

    ShareBottomSheet.show(
      context,
      shareText: shareText,
      shareUrl: shareUrl,
    );
  }

  void _navigateToGive() {
    if (widget.viewerUsername == null) {
      Fluttertoast.showToast(msg: 'Please login to give money');
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => ProfileScreen(
          fromUsername: widget.viewerUsername!,
          toUsername: widget.username,
        ),
      ),
    );
  }

  void _navigateToCashOut() {
    if (widget.viewerUsername == null) {
      Fluttertoast.showToast(msg: 'Please login to cash out');
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => CashOutScreen(username: widget.viewerUsername!),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final displayName = user['display_name'] ?? '';
    final shortHandle = widget.username.startsWith('#\$')
        ? widget.username
        : "#\${widget.username}";

    return Scaffold(
      appBar: AppBar(
        title: Text('$shortHandle Profile'),
        actions: [
          // Share button will be added later
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              _shareProfile();
            },
          ),
        ],
      ),
      body: Container(
        color: AppColors.roseGold,
        child: loading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  Expanded(
                    child: Center(
                      child: ConstrainedBox(
                        constraints: const BoxConstraints(maxWidth: 800),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Left Column - Profile Info
                              Expanded(
                                flex: 1,
                                child: Column(
                                  children: [
                                    // Profile Picture
                                    _buildProfilePicture(),
                                    const SizedBox(height: 16),

                                    // User Info
                                    Text(
                                      shortHandle,
                                      style: AppTextStyles.title,
                                      textAlign: TextAlign.center,
                                    ),
                                    if (displayName.isNotEmpty) ...[
                                      const SizedBox(height: 8),
                                      Text(
                                        displayName,
                                        style: AppTextStyles.bodyLarge,
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                    const SizedBox(height: 16),

                                    // Category
                                    if (user['category'] != null &&
                                        user['category']
                                            .toString()
                                            .isNotEmpty) ...[
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 12, vertical: 6),
                                        decoration: BoxDecoration(
                                          color: Colors.white.withOpacity(0.2),
                                          borderRadius:
                                              BorderRadius.circular(20),
                                        ),
                                        child: Text(
                                          user['category'].toString(),
                                          style: AppTextStyles.bodyMedium,
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                    ],

                                    // Bio (will be added later)
                                    // URLs (will be added later)
                                  ],
                                ),
                              ),

                              const SizedBox(width: 24),

                              // Right Column - Actions
                              Expanded(
                                flex: 1,
                                child: Column(
                                  children: [
                                    const SizedBox(height: 40),

                                    // Give Money Button
                                    SizedBox(
                                      width: double.infinity,
                                      child: ElevatedButton(
                                        style: AppButtonStyles.primaryButton,
                                        onPressed: _navigateToGive,
                                        child: const Text('Give \$0.25'),
                                      ),
                                    ),

                                    const SizedBox(height: 16),

                                    // Cash Out Button (only show if viewing own profile)
                                    if (widget.viewerUsername ==
                                        widget.username) ...[
                                      SizedBox(
                                        width: double.infinity,
                                        child: ElevatedButton(
                                          style:
                                              AppButtonStyles.secondaryButton,
                                          onPressed: _navigateToCashOut,
                                          child: const Text('Cash Out'),
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildProfilePicture() {
    final profilePictureUrl = user['profile_picture']?.toString();

    if (profilePictureUrl != null && profilePictureUrl.isNotEmpty) {
      if (profilePictureUrl.startsWith('data:image')) {
        // Base64 image
        try {
          final base64Data = profilePictureUrl.split(',')[1];
          final bytes = base64Decode(base64Data);
          return CircleAvatar(
            radius: 60,
            backgroundImage: MemoryImage(bytes),
          );
        } catch (e) {
          return _buildDefaultAvatar();
        }
      } else {
        // Network image
        final imageUrl = profilePictureUrl.startsWith('http')
            ? profilePictureUrl
            : '${Api.base}$profilePictureUrl';

        return CircleAvatar(
          radius: 60,
          child: CachedNetworkImage(
            imageUrl: imageUrl,
            imageBuilder: (context, imageProvider) => CircleAvatar(
              radius: 60,
              backgroundImage: imageProvider,
            ),
            placeholder: (context, url) => const CircularProgressIndicator(),
            errorWidget: (context, url, error) => _buildDefaultAvatar(),
          ),
        );
      }
    }

    return _buildDefaultAvatar();
  }

  Widget _buildDefaultAvatar() {
    return const CircleAvatar(
      radius: 60,
      backgroundColor: Colors.grey,
      child: Icon(Icons.person, size: 60, color: Colors.white),
    );
  }
}
