const express = require('express');
const router = express.Router();
const Stripe = require('stripe');
const stripe = Stripe(process.env.STRIPE_SECRET_KEY);
const models = require('../models');
const { db, recordPayment, findUserByUsername } = require('../db');
const { v4: uuidv4 } = require('uuid');


// ============================
// Create Stripe Checkout session
// ============================

// Checkout route
router.post('/checkout', async (req, res) => {
  try {
    const { username, amount, baseUrl, successUrl, cancelUrl } = req.body;
    console.log("Received username:", username);
    console.log("Received amount:", amount);
    console.log("Received baseUrl from frontend:", baseUrl);
    console.log("Received successUrl:", successUrl);
    console.log("Received cancelUrl:", cancelUrl);
    if (!username || !amount || !baseUrl) {
      return res.status(400).json({ error: 'username, amount, and baseUrl are required' });
    }

    const user = findUserByUsername(username);
    if (!user) return res.status(404).json({ error: 'User not found' });
    // Apply 10% platform fee on top-up - user gets 90% of what they pay
    const amountWithFee = amount;


    // Create Stripe Checkout session
    const session = await stripe.checkout.sessions.create({
      mode: 'payment',
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: { name: `Top-up for ${user.username}` },
            unit_amount: amountWithFee,
          },
          quantity: 1,
        },
      ],
      metadata: {
        userId: user.id,
        username: user.username,
      },
      success_url: `${baseUrl}/success?session_id={CHECKOUT_SESSION_ID}&amount=${amount}`,
      cancel_url: `${baseUrl}/cancel?reason=User cancelled the payment`,

    });

    // Don't record payment history here - wait for webhook confirmation
    // This prevents failed payments from creating history records

    console.log(`✅ Created checkout session ${session.id} for ${username}, balance will be updated after payment confirmation`);

    // Return session URL
    res.json({ url: session.url });
  } catch (err) {
    console.error('Error creating checkout session:', err);
    res.status(500).json({ error: err.message });
  }
});
// ============================
// Success redirect
// ============================
router.get('/success', async (req, res) => {
  try {
    const sessionId = req.query.session_id;
    if (!sessionId) return res.status(400).json({ error: 'Missing session_id' });

    const session = await stripe.checkout.sessions.retrieve(sessionId);
    const amount = session.amount_total || 0;

    // Don't update balance here - let webhook handle it
    console.log(`✅ Payment completed for session ${sessionId}, amount: ${amount} cents`);
    console.log(`⏳ Balance will be updated via webhook`);

    const isWeb = req.headers.accept && req.headers.accept.includes('text/html');
    if (isWeb) {
      const baseUrl = req.headers.referer?.split('#')[0] || req.headers.origin || 'http://localhost:4242/';
      return res.redirect(`${baseUrl}#/success?amount=${amount}&session_id=${encodeURIComponent(sessionId)}`);
    } else {
      return res.redirect(`hashtagdollars://payment-success?amount=${amount}&session_id=${encodeURIComponent(sessionId)}`);
    }
  } catch (e) {
    console.error('Success redirect error', e);
    res.status(500).json({ error: 'Error retrieving session' });
  }
});

// ============================
// Cancel page
// ============================
router.get('/cancel', (req, res) => res.send('Payment canceled.'));

// ============================
// Stripe webhook
// ============================
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET);
  } catch (err) {
    console.error('❌ Webhook signature failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  console.log('🔄 Webhook received:', event.type);

  if (event.type === 'checkout.session.completed') {
    const session = event.data.object;
    try {
      const userId = session.metadata?.userId;
      const username = session.metadata?.username;
      const amount = session.amount_total || 0;

      console.log(`💰 Processing payment: userId=${userId}, username=${username}, amount=${amount} cents`);

      if (userId && amount > 0) {
        // Check for duplicate processing using session_id
        const existingPayment = db.prepare('SELECT id FROM payment_history WHERE session_id = ?').get(session.id);
        if (existingPayment) {
          console.log(`⚠️ Payment already processed for session ${session.id}`);
          res.json({ received: true, duplicate: true });
          return;
        }

        const user = db.prepare('SELECT balance FROM users WHERE id = ?').get(userId);
        if (user) {
          console.log(`📊 Current balance for user ${username}: ${user.balance} cents`);

          // Use transaction for atomic operation
          db.transaction(() => {
            // Apply 10% platform fee - user gets 90% of what they paid
            const amountAfterFee = Math.floor(amount * 0.9);
            // Update user balance
            models.creditUser(userId, amountAfterFee);

            // Record payment in history (now that it's confirmed)
            db.prepare(
              `INSERT INTO payment_history (username, amount, session_id, created_at)
               VALUES (?, ?, ?, ?)`
            ).run(username, amountAfterFee, session.id, Date.now());

            // Create transaction record
            models.createTransaction({
              from: null,
              to: userId,
              amount: amountAfterFee,
              type: 'topup',
              note: `Stripe top-up via webhook - Session: ${session.id} (10% platform fee applied)`
            });
          })();

          console.log(`✅ Payment processing completed for session ${session.id}`);
        } else {
          console.error(`❌ User not found: ${userId}`);
        }
      } else {
        console.error(`❌ Invalid webhook data: userId=${userId}, amount=${amount}`);
      }
    } catch (e) {
      console.error('❌ Webhook DB update error:', e);
    }
  }

  res.json({ received: true });
});

// ============================
// Get amount after success
// ============================
router.get('/getamountonsuccess', async (req, res) => {
  const sessionId = req.query.session_id;
  if (!sessionId) return res.status(400).json({ error: 'Missing session_id' });

  try {
    // First check payment_history (if webhook processed)
    const row = db.prepare(`SELECT amount FROM payment_history WHERE session_id = ?`).get(sessionId);
    if (row) {
      // Return the amount after fee (what user actually received)
      return res.json({ amount: row.amount });
    }

    // If not in history, get from Stripe session (fallback)
    const session = await stripe.checkout.sessions.retrieve(sessionId);
    const amount = session.amount_total || 0;
    // Return the amount after fee (what user actually received)
    res.json({ amount: Math.floor(amount * 0.9) });
  } catch (err) {
    console.error('Error fetching payment amount:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});



// ============================
// Record payment in history + update cumulative payments
// ============================



// POST /api/payments/record
router.post('/record', async (req, res) => {
  const { userId, amount, sessionId } = req.body;
  if (!userId || !amount || !sessionId) {
    return res.status(400).json({ error: 'userId, amount, and sessionId are required' });
  }

  try {
    recordPayment(userId, amount, sessionId);
    res.json({ success: true, userId, amount, sessionId });
  } catch (err) {
    console.error('Error recording payment:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});


// ============================
// Get full payment history for a user
// ============================
/**
 * GET /api/payments/history/:username
 * Fetch all payment history records for a given username.
 * Example URL: /api/payments/history/%23%24shaban
 */
router.get('/history/:username', (req, res) => {
  try {
    let { username } = req.params; // get username from URL parameter

    if (!username) {
      return res.status(400).json({ error: 'username is required' });
    }

    // Decode URL-encoded characters (%23%24 -> #$)
    username = decodeURIComponent(username);

    // Display the username in the Windows command prompt
    console.log('Fetching payment history for username:', username);

    // Query the payment_history table using the `username` column
    const history = db.prepare(
      `SELECT id, username, amount, session_id, created_at
       FROM payment_history
       WHERE username = ?
       ORDER BY created_at DESC`
    ).all(username);

    // Respond with JSON array
    res.json({ payments: history });
  } catch (err) {
    console.error('Error fetching payment history:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});




// ============================
// Get current cumulative balance
// ============================
// ============================
// Get current cumulative balance
// ============================
router.get('/balance/:username', (req, res) => {
  try {
    let { username } = req.params;

    if (!username) return res.status(400).json({ error: 'username is required' });

    // Decode URL-encoded characters (%23%24 -> #$)
    username = decodeURIComponent(username);

    console.log('Fetching balance for username:', username); // display in CMD

    // Query the users table by username
    const row = db.prepare(`SELECT balance FROM users WHERE username = ?`).get(username);

    res.json({ balance: row ? row.balance : 0 });
  } catch (err) {
    console.error('Error fetching balance:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});





module.exports = router;
