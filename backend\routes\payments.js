const express = require('express');
const router = express.Router();
const Stripe = require('stripe');
const stripe = Stripe(process.env.STRIPE_SECRET_KEY);
const models = require('../models');
const { db, recordPayment, findUserByUsername } = require('../db');
const { v4: uuidv4 } = require('uuid');


// ============================
// Create Stripe Checkout session
// ============================

// Checkout route
router.post('/checkout', async (req, res) => {
  try {
    const { username, amount, baseUrl, successUrl, cancelUrl } = req.body;
    console.log("Received username:", username);
    console.log("Received amount:", amount);
    console.log("Received baseUrl from frontend:", baseUrl);
    console.log("Received successUrl:", successUrl);
    console.log("Received cancelUrl:", cancelUrl);
    if (!username || !amount || !baseUrl) {
      return res.status(400).json({ error: 'username, amount, and baseUrl are required' });
    }

    const user = findUserByUsername(username);
    if (!user) return res.status(404).json({ error: 'User not found' });
    // No fees on top-up - user gets full amount
    const amountWithFee = amount;


    // Create Stripe Checkout session
    const session = await stripe.checkout.sessions.create({
      mode: 'payment',
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: { name: `Top-up for ${user.username}` },
            unit_amount: amountWithFee,
          },
          quantity: 1,
        },
      ],
      metadata: {
        userId: user.id,
        username: user.username,
      },
      success_url: `${baseUrl}/success?session_id={CHECKOUT_SESSION_ID}&amount=${amount}`,
      cancel_url: `${baseUrl}/cancel?reason=User cancelled the payment`,

    });

    // Record payment intent in history (but don't update balance yet)
    db.prepare(
      `INSERT INTO payment_history (username, amount, session_id, created_at)
       VALUES (?, ?, ?, ?)`
    ).run(username, amount, session.id, Date.now());

    console.log(`✅ Created checkout session ${session.id} for ${username}, balance will be updated after payment confirmation`);

    // Return session URL
    res.json({ url: session.url });
  } catch (err) {
    console.error('Error creating checkout session:', err);
    res.status(500).json({ error: err.message });
  }
});
// ============================
// Success redirect
// ============================
router.get('/success', async (req, res) => {
  try {
    const sessionId = req.query.session_id;
    if (!sessionId) return res.status(400).json({ error: 'Missing session_id' });

    const session = await stripe.checkout.sessions.retrieve(sessionId);
    const amount = session.amount_total || 0;

    // Check if payment was successful and balance hasn't been updated yet
    if (session.payment_status === 'paid') {
      const userId = session.metadata?.userId;
      const username = session.metadata?.username;

      console.log(`💰 Success callback: session=${sessionId}, userId=${userId}, username=${username}, amount=${amount} cents`);

      const existingPayment = db.prepare('SELECT id FROM payment_history WHERE session_id = ? AND amount > 0').get(sessionId);

      if (existingPayment) {
        // Check if balance has already been updated for this session
        const user = db.prepare('SELECT balance FROM users WHERE id = ?').get(userId);

        if (user && user.balance >= amount) {
          console.log(`✅ Balance already updated for session ${sessionId} (current balance: ${user.balance} cents)`);
        } else {
          // Update balance as fallback (webhook should handle this normally)
          console.log(`🔄 Updating balance via success callback for session ${sessionId} (fallback)`);
          db.prepare('UPDATE users SET balance = COALESCE(balance, 0) + ? WHERE id = ?').run(amount, userId);

          // Create transaction record
          models.createTransaction({
            from: null,
            to: userId,
            amount: amount,
            type: 'topup',
            note: `Stripe top-up via success callback - Session: ${sessionId}`
          });

          console.log(`✅ Balance updated via success callback: +${amount} cents for user ${username}`);
        }
      } else {
        console.error(`❌ No payment record found for session ${sessionId}`);
      }
    } else {
      console.log(`⚠️ Payment not completed for session ${sessionId}, status: ${session.payment_status}`);
    }

    const isWeb = req.headers.accept && req.headers.accept.includes('text/html');
    if (isWeb) {
      const baseUrl = req.headers.referer?.split('#')[0] || req.headers.origin || 'http://localhost:4242/';
      return res.redirect(`${baseUrl}#/success?amount=${amount}&session_id=${encodeURIComponent(sessionId)}`);
    } else {
      return res.redirect(`hashtagdollars://payment-success?amount=${amount}&session_id=${encodeURIComponent(sessionId)}`);
    }
  } catch (e) {
    console.error('Success redirect error', e);
    res.status(500).json({ error: 'Error retrieving session' });
  }
});

// ============================
// Cancel page
// ============================
router.get('/cancel', (req, res) => res.send('Payment canceled.'));

// ============================
// Stripe webhook
// ============================
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET);
  } catch (err) {
    console.error('❌ Webhook signature failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  console.log('🔄 Webhook received:', event.type);

  if (event.type === 'checkout.session.completed') {
    const session = event.data.object;
    try {
      const userId = session.metadata?.userId;
      const username = session.metadata?.username;
      const amount = session.amount_total || 0;

      console.log(`💰 Processing payment: userId=${userId}, username=${username}, amount=${amount} cents`);

      if (userId && amount > 0) {
        // Check if balance has already been updated to prevent double charging
        const user = db.prepare('SELECT balance FROM users WHERE id = ?').get(userId);
        if (user) {
          console.log(`📊 Current balance for user ${username}: ${user.balance} cents`);

          // Update user balance
          models.creditUser(userId, amount);
          console.log(`✅ Balance updated: +${amount} cents for user ${username}`);

          // Create transaction record
          models.createTransaction({
            from: null,
            to: userId,
            amount: amount,
            type: 'topup',
            note: `Stripe top-up via webhook - Session: ${session.id}`
          });

          // Payment status tracking not needed for current simple payments table
          // models.updatePaymentStatusByReference(session.id, 'succeeded');

          console.log(`✅ Payment processing completed for session ${session.id}`);
        } else {
          console.error(`❌ User not found: ${userId}`);
        }
      } else {
        console.error(`❌ Invalid webhook data: userId=${userId}, amount=${amount}`);
      }
    } catch (e) {
      console.error('❌ Webhook DB update error:', e);
    }
  }

  res.json({ received: true });
});

// ============================
// Get amount after success
// ============================
router.get('/getamountonsuccess', (req, res) => {
  const sessionId = req.query.session_id;
  if (!sessionId) return res.status(400).json({ error: 'Missing session_id' });

  try {
    const row = db.prepare(`SELECT amount FROM payment_history WHERE session_id = ?`).get(sessionId);
    if (!row) return res.status(404).json({ error: 'Payment session not found' });
    res.json({ amount: row.amount });
  } catch (err) {
    console.error('Error fetching payment amount:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});



// ============================
// Record payment in history + update cumulative payments
// ============================



// POST /api/payments/record
router.post('/record', async (req, res) => {
  const { userId, amount, sessionId } = req.body;
  if (!userId || !amount || !sessionId) {
    return res.status(400).json({ error: 'userId, amount, and sessionId are required' });
  }

  try {
    recordPayment(userId, amount, sessionId);
    res.json({ success: true, userId, amount, sessionId });
  } catch (err) {
    console.error('Error recording payment:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});


// ============================
// Get full payment history for a user
// ============================
/**
 * GET /api/payments/history/:username
 * Fetch all payment history records for a given username.
 * Example URL: /api/payments/history/%23%24shaban
 */
router.get('/history/:username', (req, res) => {
  try {
    let { username } = req.params; // get username from URL parameter

    if (!username) {
      return res.status(400).json({ error: 'username is required' });
    }

    // Decode URL-encoded characters (%23%24 -> #$)
    username = decodeURIComponent(username);

    // Display the username in the Windows command prompt
    console.log('Fetching payment history for username:', username);

    // Query the payment_history table using the `username` column
    const history = db.prepare(
      `SELECT id, username, amount, session_id, created_at
       FROM payment_history
       WHERE username = ?
       ORDER BY created_at DESC`
    ).all(username);

    // Respond with JSON array
    res.json({ payments: history });
  } catch (err) {
    console.error('Error fetching payment history:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});




// ============================
// Get current cumulative balance
// ============================
// ============================
// Get current cumulative balance
// ============================
router.get('/balance/:username', (req, res) => {
  try {
    let { username } = req.params;

    if (!username) return res.status(400).json({ error: 'username is required' });

    // Decode URL-encoded characters (%23%24 -> #$)
    username = decodeURIComponent(username);

    console.log('Fetching balance for username:', username); // display in CMD

    // Query the payments table by username
    const row = db.prepare(`SELECT balance FROM users WHERE username = ?`).get(username);

    res.json({ balance: row ? row.amount : 0 });
  } catch (err) {
    console.error('Error fetching balance:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});





module.exports = router;
