// lib/screens/profile_screen.dart
import 'dart:convert';
import 'package:universal_html/html.dart' as html;
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:provider/provider.dart';
import 'package:hashtag_dollars/screens/social_profile_screen.dart';
import 'package:hashtag_dollars/widgets/enhanced_share_widget.dart';
import 'package:http/http.dart' as http;
import '../constants/theme_constants.dart';
import '../services/api.dart';
import '../services/balance_provider.dart';
import 'cashout_screen.dart';

class GiveCashOutScreen extends StatefulWidget {
  final String fromUsername; // logged-in user
  final String toUsername; // clicked user

  const GiveCashOutScreen({
    super.key,
    required this.fromUsername,
    required this.toUsername,
  });

  @override
  State<GiveCashOutScreen> createState() => _GiveCashOutScreenState();
}

class _GiveCashOutScreenState extends State<GiveCashOutScreen> {
  Map<String, dynamic> user = {};
  bool loading = true;
  double customAmount = 0.25;
  Uint8List? profileImageBytes;

  // donation history + favorites
  List<Map<String, dynamic>> givers30Days = [];
  double totalReceived30Days = 0.0;
  List<Map<String, dynamic>> favoriteGivers = [];

  bool showShareIcons = false; // after donation

  @override
  void initState() {
    super.initState();
    _load();
    _loadDonationHistory();
    _loadFavorites();
  }

  void _showShareBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.cardBackground,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => EnhancedShareWidget(
        contentId: widget.toUsername,
        contentText:
            user['bio'] ?? user['display_name'] ?? 'Check out this profile',
        contentType: 'profile',
      ),
    );
  }

  void _showShareGiveMsg() {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.cardBackground,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => EnhancedShareWidget(
        contentId: widget.toUsername,
        contentText:
            'I just made a Micro Donation to ${widget.toUsername} and you should too!',
        contentType: 'give',
      ),
    );
  }

  Future<void> _load() async {
    setState(() => loading = true);
    try {
      final u = await Api.getUserByUsername(widget.toUsername);
      setState(() {
        user = u ?? {};
      });

      // Refresh balance from provider
      final balanceProvider =
          Provider.of<BalanceProvider>(context, listen: false);
      await balanceProvider.refreshBalance();

      // Handle profile picture
      final profilePictureUrl = user['profile_picture']?.toString();
      if (profilePictureUrl != null && profilePictureUrl.isNotEmpty) {
        if (profilePictureUrl.startsWith('data:image')) {
          // It's a base64 data URL, decode it
          try {
            final base64Data = profilePictureUrl.split(',')[1];
            final bytes = base64Decode(base64Data);
            setState(() => profileImageBytes = bytes);
          } catch (e) {
            debugPrint('Error decoding profile picture: $e');
          }
        } else if (!profilePictureUrl.startsWith('http')) {
          // If it's a relative path, construct full URL
          final fullUrl = '${Api.base}${profilePictureUrl}';

          // Preload image bytes for Web
          try {
            final pictureRes = await http.get(Uri.parse(fullUrl));
            if (pictureRes.statusCode == 200) {
              setState(() => profileImageBytes = pictureRes.bodyBytes);
            }
          } catch (e) {
            debugPrint('Error loading profile picture: $e');
          }
        }
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error: $e');
    } finally {
      setState(() => loading = false);
    }
  }

  Future<void> _loadDonationHistory() async {
    try {
      final response = await http.get(Uri.parse(
          "${Api.baseUrl}/api/users/${widget.toUsername}/donations30days"));
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          givers30Days = List<Map<String, dynamic>>.from(data['givers'] ?? []);
          totalReceived30Days = ((data['total'] ?? 0) as int) / 100.0;
        });
      }
    } catch (_) {}
  }

  Future<void> _loadFavorites() async {
    try {
      final response = await http.get(Uri.parse(
          "${Api.baseUrl}/api/users/${widget.fromUsername}/favorites"));
      if (response.statusCode == 200) {
        setState(() {
          favoriteGivers =
              List<Map<String, dynamic>>.from(json.decode(response.body));
        });
      }
    } catch (_) {}
  }

  Future<void> _donate(double amount) async {
    if (amount <= 0) return;
    final balanceProvider =
        Provider.of<BalanceProvider>(context, listen: false);
    if (amount > balanceProvider.balance) {
      Fluttertoast.showToast(
          msg: "Insufficient balance. Please add funds first.");
      return;
    }

    try {
      // Use new balance-based donation API
      final amountCents = (amount * 100).toInt();

      final result = await Api.donateFromBalance(
        fromUsername: widget.fromUsername,
        toUsername: widget.toUsername,
        amountCents: amountCents,
        feeCents: 0, // No fees on donations
      );

      if (result?['error'] != null) {
        final error = result!['error'] as String;
        if (error.contains('Insufficient balance')) {
          Fluttertoast.showToast(
              msg: "Insufficient balance. Please add funds first.");
        } else if (error.contains('not found')) {
          Fluttertoast.showToast(
              msg: "User not found. Please check the username.");
        } else {
          Fluttertoast.showToast(msg: "Donation Error: $error");
        }
        return;
      }

      // Success!
      Fluttertoast.showToast(
          msg:
              "✅ Donation of \$${amount.toStringAsFixed(2)} sent successfully!");

      setState(() {
        showShareIcons = true;
      });

      // Update balances from API response
      final balanceProvider =
          Provider.of<BalanceProvider>(context, listen: false);
      final newBalances = result?['newBalances'] as Map<String, dynamic>?;
      final donorBalance = newBalances?['donor'] ?? 0;
      final recipientBalance = newBalances?['recipient'] ?? 0;
      
      // Update donor balance in provider
      balanceProvider.updateBalance(donorBalance / 100.0);
      
      // Update recipient balance in local user data
      setState(() {
        user['balance'] = recipientBalance;
      });

      // Refresh other data
      _loadDonationHistory();
    } catch (e) {
      Fluttertoast.showToast(msg: "Error: $e");
    }
  }

  Future<void> _navigateToCashOut() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CashOutScreen(username: widget.fromUsername),
      ),
    );

    if (result == true) {
      // Refresh balance after cash out
      final balanceProvider =
          Provider.of<BalanceProvider>(context, listen: false);
      await balanceProvider.refreshBalance();
      await _load();
    }
  }

  void _copyProfileHandle() {
    // Strip #$ prefix for cleaner sharing URLs
    final cleanUsername = widget.toUsername.startsWith('#\$')
        ? widget.toUsername.substring(2) // Remove #$ prefix
        : widget.toUsername;
    final profileUrl =
        "https://www.hashtagdollars.com/social-profile?username=${Uri.encodeComponent(cleanUsername)}";

    html.window.navigator.clipboard?.writeText(profileUrl);
    Fluttertoast.showToast(msg: "Profile link copied!");
  }

  void _addToFavorites(Map<String, dynamic> giver) async {
    try {
      final response = await http.post(
        Uri.parse("${Api.baseUrl}/api/users/${widget.fromUsername}/favorites"),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode({"giverUsername": giver['username']}),
      );
      if (response.statusCode == 200) {
        Fluttertoast.showToast(msg: "Added to favorites!");
        _loadFavorites();
      }
    } catch (_) {}
  }

  @override
  Widget build(BuildContext context) {
    final display = user['display_name'] ?? '';
    final shortHandle = widget.toUsername.startsWith('#\$')
        ? widget.toUsername
        : "#\${widget.toUsername}";

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.toUsername),
        actions: [
          // Go to social Profile
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => SocialProfileScreen(
                      fromUsername: widget.fromUsername,
                      toUsername: widget.toUsername),
                ),
              );
            },
            icon: const Icon(Icons.swap_horizontal_circle_sharp),
          ),
          IconButton(
            icon: const Icon(Icons.share),
            tooltip: 'Share Profile',
            onPressed: _showShareBottomSheet,
          ),
        ],
      ),
      body: Container(
        color: AppColors.roseGold, // Rose Gold background
        child: Column(
          children: [
            Expanded(
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 600),
                  child: loading
                      ? const Center(child: CircularProgressIndicator())
                      : RefreshIndicator(
                          onRefresh: () async {
                            await _load();
                            await _loadDonationHistory();
                            await _loadFavorites();
                          },
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: SingleChildScrollView(
                              physics: const AlwaysScrollableScrollPhysics(),
                              child: Column(
                              children: [
                                // Logo
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Image.asset('assets/logo.png', height: 50),
                                    // Title
                                    const Text(
                                      'Hashtag Dollars',
                                      style: AppTextStyles.title,
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 10),
                                // Balance Display (for donor)
                                if (widget.fromUsername == widget.toUsername)
                                  Consumer<BalanceProvider>(
                                    builder: (context, balanceProvider, child) {
                                      return Container(
                                        width: double.infinity,
                                        padding: const EdgeInsets.all(16),
                                        margin:
                                            const EdgeInsets.only(bottom: 16),
                                        decoration: BoxDecoration(
                                          color: Colors.green.withOpacity(0.1),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          border:
                                              Border.all(color: Colors.green),
                                        ),
                                        child: Column(
                                          children: [
                                            Text(
                                              'Your Balance',
                                              style: AppTextStyles.bodyLarge,
                                            ),
                                            const SizedBox(height: 8),
                                            Text(
                                              '\$${balanceProvider.balance.toStringAsFixed(2)}',
                                              style:
                                                  AppTextStyles.title.copyWith(
                                                fontSize: 28,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.green,
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),

                                Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[200],
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Row(
                                    children: [
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          if (display
                                              .toString()
                                              .isNotEmpty) ...[
                                            Text(display,
                                                style: const TextStyle(
                                                    fontSize: 22,
                                                    fontWeight:
                                                        FontWeight.bold)),
                                            const SizedBox(height: 8),
                                          ],
                                          Text(
                                              'Profile Balance: \$${((user['balance'] ?? 0) / 100.0).toStringAsFixed(2)}'),
                                          const SizedBox(height: 8),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              SelectableText(shortHandle,
                                                  style: const TextStyle(
                                                      color: Colors.blue)),
                                              IconButton(
                                                icon: const Icon(Icons.copy),
                                                onPressed: _copyProfileHandle,
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                      const Spacer(),
                                      if (user['profile_picture'] != null) ...[
                                        const SizedBox(width: 16),
                                        CircleAvatar(
                                          radius: 30,
                                          backgroundImage: NetworkImage(
                                              user['profile_picture']),
                                        ),
                                      ],
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 20),

                                // // Donation history 30 days
                                // Container(
                                //   padding: const EdgeInsets.all(12),
                                //   decoration: BoxDecoration(
                                //     color: Colors.white.withOpacity(0.1),
                                //     borderRadius: BorderRadius.circular(12),
                                //   ),
                                //   child: Column(
                                //     crossAxisAlignment:
                                //         CrossAxisAlignment.start,
                                //     children: [
                                //       Text("Givers in last 30 days",
                                //           style: AppTextStyles.bodyLarge),
                                //       const SizedBox(height: 8),
                                //       for (var g in givers30Days)
                                //         ListTile(
                                //           leading: g['profile_picture'] != null
                                //               ? CircleAvatar(
                                //                   backgroundImage: NetworkImage(
                                //                       g['profile_picture']))
                                //               : const CircleAvatar(
                                //                   child: Icon(Icons.person)),
                                //           title: Text(g['username'],
                                //               style: AppTextStyles.bodyMedium),
                                //           trailing: IconButton(
                                //             icon: const Icon(
                                //                 Icons.favorite_border,
                                //                 color: Colors.black),
                                //             onPressed: () => _addToFavorites(g),
                                //           ),
                                //         ),
                                //       const SizedBox(height: 8),
                                //       Text(
                                //           "Total received: \$${totalReceived30Days.toStringAsFixed(2)}",
                                //           style: AppTextStyles.bodyMedium),
                                //     ],
                                //   ),
                                // ),
                                // const SizedBox(height: 20),

                                // Actions
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                      color: Colors.white.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12)),
                                  child: Column(
                                    children: [
                                      // Show current balance for donor
                                      Consumer<BalanceProvider>(
                                        builder:
                                            (context, balanceProvider, child) {
                                          return Container(
                                            width: double.infinity,
                                            padding: const EdgeInsets.all(12),
                                            margin: const EdgeInsets.only(
                                                bottom: 16),
                                            decoration: BoxDecoration(
                                              color:
                                                  Colors.blue.withOpacity(0.1),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            child: Text(
                                              'Your Balance: \$${balanceProvider.balance.toStringAsFixed(2)}',
                                              style: AppTextStyles.bodyLarge
                                                  .copyWith(
                                                fontWeight: FontWeight.bold,
                                              ),
                                              textAlign: TextAlign.center,
                                            ),
                                          );
                                        },
                                      ),

                                      TextField(
                                        style: AppTextStyles.inputText,
                                        keyboardType: const TextInputType
                                            .numberWithOptions(decimal: true),
                                        decoration:
                                            AppInputDecorations.primaryInput(
                                          hintText: "Donation Amount (USD)",
                                        ),
                                        onChanged: (val) {
                                          setState(() {
                                            customAmount =
                                                double.tryParse(val) ?? 0.25;
                                          });
                                        },
                                      ),
                                      const SizedBox(height: 12),

                                      Consumer<BalanceProvider>(
                                        builder:
                                            (context, balanceProvider, child) {
                                          final hasEnoughBalance =
                                              customAmount > 0 &&
                                                  customAmount <=
                                                      balanceProvider.balance;
                                          return Column(
                                            children: [
                                              ElevatedButton(
                                                style: AppButtonStyles
                                                    .primaryButton
                                                    .copyWith(
                                                  backgroundColor:
                                                      MaterialStateProperty.all(
                                                    hasEnoughBalance
                                                        ? Colors.blue
                                                        : Colors.grey,
                                                  ),
                                                ),
                                                onPressed: hasEnoughBalance
                                                    ? () =>
                                                        _donate(customAmount)
                                                    : null,
                                                child: Text(
                                                  customAmount >
                                                          balanceProvider
                                                              .balance
                                                      ? "Insufficient Balance"
                                                      : "Give \$${customAmount.toStringAsFixed(2)}",
                                                ),
                                              ),
                                              if (customAmount >
                                                  balanceProvider.balance)
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          top: 8),
                                                  child: Text(
                                                    'Add funds to your account to make donations',
                                                    style: AppTextStyles
                                                        .bodySmall
                                                        .copyWith(
                                                      color:
                                                          AppColors.warningText,
                                                    ),
                                                    textAlign: TextAlign.center,
                                                  ),
                                                ),
                                            ],
                                          );
                                        },
                                      ),

                                      const SizedBox(height: 12),

                                      ElevatedButton(
                                        style: AppButtonStyles.primaryButton
                                            .copyWith(
                                          backgroundColor:
                                              MaterialStateProperty.all(
                                                  Colors.green),
                                        ),
                                        onPressed: _navigateToCashOut,
                                        child: const Text('Cash Out'),
                                      ),
                                    ],
                                  ),
                                ),

                                if (showShareIcons) ...[
                                  const SizedBox(height: 20),
                                  // Show single share icon centered
                                  IconButton(
                                    icon: const Icon(Icons.share,
                                        color: Colors.lightBlue),
                                    onPressed: _showShareGiveMsg,
                                  ),
                                ],

                                const SizedBox(height: 20),
                              ],
                              ),
                            ),
                          ),
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
